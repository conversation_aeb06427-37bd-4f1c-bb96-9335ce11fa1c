// pages/user/dashboard.js
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/app/contexts/AuthProvider";

// Import components
import Dashboard from "./_components/Dashboard";
import Profile from "./_components/Profile";
import WorkingHours from "./_components/WorkingHours";
import Services from "./_components/Services";
import Appointments from "./_components/Appointments";
import Settings from "./_components/Settings";

export default function UserDashboard() {
  const router = useRouter();
  const { user, loading, setLoading, logout, refreshUserData } = useAuth();
  const [activeSection, setActiveSection] = useState("dashboard");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    // Check for user in localStorage on initial load
    const checkUserAuth = () => {
      if (!loading) {
        if (!user) {
          router.replace("/");
        }
      }
    };

    checkUserAuth();

    // Set a reasonable timeout for loading state
    const loadingTimeout = setTimeout(() => {
      setLoading(false);
    }, 3000);

    return () => clearTimeout(loadingTimeout);
  }, [user, loading, router]);

  // Refresh user data when the component mounts
  useEffect(() => {
    if (user && !loading) {
      refreshUserData();
    }
  }, []);

  if (loading) {
    return (
      <p className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        Učitavanje...
      </p>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex flex-col items-center justify-center">
        <h1 className="text-2xl mb-4">Vratite se na početnu stranicu</h1>
        <button
          onClick={() => router.push("/")}
          className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
        >
          Početna stranica
        </button>
      </div>
    );
  }

  const sidebarItems = [
    { id: "dashboard", label: "Kontrolna tabla", icon: "📊" },
    { id: "profile", label: "Profil", icon: "👤" },
    { id: "workingHours", label: "Radno vreme", icon: "🕒" },
    { id: "services", label: "Usluge", icon: "💼" },
    { id: "appointments", label: "Zakazivanja", icon: "📅" },
    { id: "settings", label: "Podešavanja", icon: "⚙️" },
  ];

  const renderMainContent = () => {
    switch (activeSection) {
      case "dashboard":
        return <Dashboard user={user} setActiveSection={setActiveSection} />;
      case "profile":
        return <Profile user={user} />;
      case "workingHours":
        return <WorkingHours user={user} />;
      case "services":
        return <Services user={user} />;
      case "appointments":
        return <Appointments user={user} />;
      case "settings":
        return <Settings user={user} />;
      default:
        return <p>Izaberite sekciju iz menija</p>;
    }
  };

  return (
    <div className="min-h-screen bg-[var(--background)] flex">
      {/* Desktop Sidebar */}
      <div className="w-64 bg-[var(--surface)] shadow-sm hidden md:block">
        <div className="p-4">
          <h2 className="text-xl font-bold text-[var(--foreground)] truncate">
            {user?.user.businessName}
          </h2>
          <p className="text-sm text-[var(--foreground)]/70 mt-1 truncate">
            {user?.user.email}
          </p>
        </div>
        <nav className="mt-4">
          <ul>
            {sidebarItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                    activeSection === item.id
                      ? "bg-[var(--surface-hover)] text-[var(--foreground)]"
                      : "text-[var(--foreground)]/70 hover:bg-[var(--surface-hover)] hover:text-[var(--foreground)]"
                  } transition-colors`}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span>{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm"
            onClick={() => setMobileMenuOpen(false)}
          ></div>

          {/* Sidebar Menu */}
          <div className="fixed inset-y-0 left-0 w-[80%] max-w-sm bg-[var(--surface)] shadow-xl overflow-y-auto z-50">
            <div className="p-4 border-b border-[var(--foreground)]/10">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-bold text-[var(--foreground)] truncate">
                  {user?.user.businessName}
                </h2>
                <button
                  onClick={() => setMobileMenuOpen(false)}
                  className="p-2 text-[var(--foreground)] hover:bg-[var(--surface-hover)] rounded-md transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-[var(--foreground)]/70 truncate">
                {user?.user.email}
              </p>
            </div>
            <nav className="mt-2">
              <ul>
                {sidebarItems.map((item) => (
                  <li key={item.id}>
                    <button
                      onClick={() => {
                        setActiveSection(item.id);
                        setMobileMenuOpen(false);
                      }}
                      className={`w-full text-left px-4 py-4 flex items-center space-x-3 ${
                        activeSection === item.id
                          ? "bg-[var(--surface-hover)] text-[var(--foreground)] border-r-2 border-teal-500"
                          : "text-[var(--foreground)]/70 hover:bg-[var(--surface-hover)] hover:text-[var(--foreground)]"
                      } transition-colors`}
                    >
                      <span className="text-xl flex-shrink-0">{item.icon}</span>
                      <span className="font-medium">{item.label}</span>
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="p-4 mt-4 border-t border-[var(--foreground)]/10">
              <button
                onClick={() => {
                  logout();
                  setMobileMenuOpen(false);
                }}
                className="w-full px-4 py-3 rounded-md bg-red-50 text-red-700 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/40 transition-colors flex items-center justify-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                <span>Odjavi se</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-[var(--surface)] shadow-sm flex-shrink-0">
          <div className="px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 flex justify-between items-center">
            <div className="flex items-center">
              {/* Mobile menu button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden mr-3 p-2 text-[var(--foreground)] hover:bg-[var(--surface-hover)] rounded-md transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={
                      mobileMenuOpen
                        ? "M6 18L18 6M6 6l12 12"
                        : "M4 6h16M4 12h16M4 18h16"
                    }
                  />
                </svg>
              </button>
              <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-[var(--foreground)] truncate">
                Zakaži AI Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <span className="hidden sm:block text-[var(--foreground)]/70 text-sm sm:text-base truncate max-w-32 sm:max-w-none">
                {user?.user.name}
              </span>
              <button
                onClick={logout}
                className="px-2 py-1 sm:px-4 sm:py-2 text-sm sm:text-base rounded-md text-[var(--foreground)]/80 hover:text-[var(--foreground)] hover:bg-[var(--surface-hover)] transition-colors"
              >
                <span className="hidden sm:inline">Odjavi se</span>
                <span className="sm:hidden">Odjava</span>
              </button>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
            <div className="bg-[var(--surface)] rounded-lg shadow p-3 sm:p-4 md:p-6">
              {renderMainContent()}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
