// app/book/[slug]/page.jsx
"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import HybridBookingForm from "@/app/user/_components/HybridBookingForm";

// This page needs to be client component to handle the booking completion
export default function BookingPage({ params }) {
  // Safely extract the slug using React.use()
  const unwrappedParams = use(params);
  const slug = unwrappedParams?.slug || "";

  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [services, setServices] = useState([]);
  const [error, setError] = useState("");
  const [bookingComplete, setBookingComplete] = useState(false);

  // Load user and services data
  useEffect(() => {
    if (!slug) return; // Only proceed if we have a slug

    const fetchData = async () => {
      try {
        setLoading(true);
        // Fetch user data
        const response = await fetch(`/api/users/get-by-slug?slug=${slug}`);

        if (!response.ok) {
          throw new Error("Salon nije pronađen");
        }

        const userData = await response.json();
        setUser(userData.user);

        // Fetch services or use the ones from user data
        if (userData.user) {
          if (userData.user.services && userData.user.services.length > 0) {
            setServices(userData.user.services);
          } else {
            // Optionally fetch services separately if needed
            const servicesResponse = await fetch(
              `/api/services/get-all?userId=${userData.user._id}`
            );
            if (servicesResponse.ok) {
              const servicesData = await servicesResponse.json();
              setServices(servicesData.services || []);
            }
          }
        }
      } catch (err) {
        console.error("Error loading user data:", err);
        setError(err.message || "Greška pri učitavanju podataka");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [slug]);

  // Handle booking completion
  const handleBookingComplete = (success) => {
    if (success) {
      setBookingComplete(true);
      // Optionally redirect after a successful booking
      setTimeout(() => {
        if (slug) {
          router.push(`/book/${slug}/success`);
        }
      }, 3000);
    }
  };

  if (loading) {
    return (
      <div className="p-8 max-w-lg mx-auto text-center">
        <p>Učitavanje...</p>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="p-8 max-w-lg mx-auto">
        <p className="text-red-500">{error || "Salon nije pronađen."}</p>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4 text-center">
        Rezervišite termin za {user.businessName || user.name}
      </h1>

      {bookingComplete ? (
        <div className="bg-green-100 p-4 rounded-md text-green-700 text-center">
          <p className="font-medium">Termin je uspešno zakazan!</p>
          <p className="text-sm mt-2">
            Prebacujemo vas na stranicu sa potvrdom...
          </p>
        </div>
      ) : (
        <>
          {user.businessDescription && (
            <div className="mb-6 text-center text-gray-600 dark:text-gray-300">
              <p>{user.businessDescription}</p>
            </div>
          )}

          {user.address && (
            <div className="mb-6 text-center text-sm text-gray-500 dark:text-gray-400">
              <p>Lokacija: {user.address}</p>
            </div>
          )}

          <HybridBookingForm
            user={user}
            services={services}
            onComplete={handleBookingComplete}
          />

          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 text-center text-sm text-gray-500 dark:text-gray-400">
            <p>Za sve dodatne informacije možete nas kontaktirati telefonom</p>
            {user.phone && <p className="font-medium">{user.phone}</p>}
          </div>
        </>
      )}
    </div>
  );
}
