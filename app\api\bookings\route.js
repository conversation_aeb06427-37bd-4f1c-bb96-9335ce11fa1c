// app/api/bookings/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import Booking from "../../auth/models/Booking";

export async function GET(request) {
  await dbConnect();

  // Get the URL object to extract search params
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");

  if (!userId) {
    return NextResponse.json(
      { error: "Obavezan je ID korisnika" },
      { status: 400 }
    );
  }

  try {
    // Pronađi zakazivanja direktno iz Booking modela
    const bookings = await Booking.find({ userId }).sort({ date: -1 }).lean();

    // Vrati zakazivanja
    return NextResponse.json({
      bookings: bookings || [],
    });
  } catch (error) {
    console.error("Greška pri dobavljanju zakazivanja:", error);
    return NextResponse.json({ error: "<PERSON><PERSON>ška na serveru" }, { status: 500 });
  }
}
