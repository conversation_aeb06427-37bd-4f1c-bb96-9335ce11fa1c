"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/app/contexts/AuthProvider";

// Import admin components
import AdminDashboard from "./_components/AdminDashboard";
import RequestsManager from "./_components/RequestsManager";
import AIUsageStats from "./_components/AIUsageStats";
import UsersManager from "./_components/UsersManager";
import SystemCleanup from "./_components/SystemCleanup";
import RegisterForm from "./_components/RegisterForm";

export default function AdminDashboardPage() {
  const router = useRouter();
  const { user, loading, logout } = useAuth();
  const [activeSection, setActiveSection] = useState("dashboard");
  const [showRegisterForm, setShowRegisterForm] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Admin sidebar items
  const sidebarItems = [
    { id: "dashboard", label: "Ko<PERSON>rolna tabla", icon: "📊" },
    { id: "requests", label: "<PERSON>ahte<PERSON>", icon: "📋" },
    { id: "users", label: "Korisnici", icon: "👥" },
    { id: "analytics", label: "AI Analitika", icon: "🤖" },
    { id: "cleanup", label: "Čišćenje", icon: "🧹" },
  ];

  const renderMainContent = () => {
    switch (activeSection) {
      case "dashboard":
        return (
          <AdminDashboard user={user} setActiveSection={setActiveSection} />
        );
      case "requests":
        return <RequestsManager />;
      case "users":
        return <UsersManager setShowRegisterForm={setShowRegisterForm} />;
      case "analytics":
        return <AIUsageStats />;
      case "cleanup":
        return <SystemCleanup />;
      default:
        return <p>Izaberite sekciju iz menija</p>;
    }
  };

  // Ako još učitava auth ili nije admin, preusmeri ili prikaži poruku
  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.replace("/");
      }
    }
  }, [user, loading]);

  if (!user) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex flex-col items-center justify-center">
        <h1 className="text-2xl mb-4 text-[var(--foreground)]">
          Nemate pristup
        </h1>
        <button
          onClick={() => router.push("/")}
          className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
        >
          Vratite se na početnu
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-500 mx-auto mb-4"></div>
          <p className="text-[var(--foreground)]/70">Učitavanje...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[var(--background)] flex flex-col sm:flex-row">
      {/* Desktop Sidebar */}
      <div className="w-64 bg-[var(--surface)] shadow-sm hidden md:block relative">
        <div className="p-4">
          <h2 className="text-xl font-bold text-[var(--foreground)]">
            Admin Panel
          </h2>
          <p className="text-sm text-[var(--foreground)]/70 mt-1">
            {user?.email || "Administrator"}
          </p>
        </div>
        <nav className="mt-4">
          <ul>
            {sidebarItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                    activeSection === item.id
                      ? "bg-[var(--surface-hover)] text-[var(--foreground)]"
                      : "text-[var(--foreground)]/70 hover:bg-[var(--surface-hover)] hover:text-[var(--foreground)]"
                  } transition-colors`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Logout button in sidebar */}
        <div className="absolute bottom-4 left-4 right-4">
          <button
            onClick={logout}
            className="w-full px-4 py-2 text-left text-[var(--foreground)]/70 hover:text-[var(--foreground)] hover:bg-[var(--surface-hover)] rounded-md transition-colors flex items-center space-x-3"
          >
            <span className="text-lg">🚪</span>
            <span>Odjavi se</span>
          </button>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden bg-[var(--surface)] shadow-sm flex-shrink-0 h-16 w-full">
        <div className="px-3 sm:px-4 py-3 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <h1 className="text-lg font-bold text-[var(--foreground)] truncate">
              Admin Panel
            </h1>
            <span className="hidden sm:block text-sm text-[var(--foreground)]/70 truncate">
              {user?.email}
            </span>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-md text-[var(--foreground)] hover:bg-[var(--surface-hover)] transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={
                  isMobileMenuOpen
                    ? "M6 18L18 6M6 6l12 12"
                    : "M4 6h16M4 12h16M4 18h16"
                }
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm"
            onClick={() => setIsMobileMenuOpen(false)}
          ></div>

          {/* Sidebar Menu */}
          <div className="fixed inset-y-0 left-0 w-[80%] max-w-sm bg-[var(--surface)] shadow-xl overflow-y-auto z-50">
            <div className="p-4 border-b border-[var(--foreground)]/10">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-bold text-[var(--foreground)] truncate">
                  Admin Panel
                </h2>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="p-2 text-[var(--foreground)] hover:bg-[var(--surface-hover)] rounded-md transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-[var(--foreground)]/70 truncate">
                {user?.email || "Administrator"}
              </p>
            </div>
            <nav className="mt-2">
              <ul>
                {sidebarItems.map((item) => (
                  <li key={item.id}>
                    <button
                      onClick={() => {
                        setActiveSection(item.id);
                        setIsMobileMenuOpen(false);
                      }}
                      className={`w-full text-left px-4 py-4 flex items-center space-x-3 ${
                        activeSection === item.id
                          ? "bg-[var(--surface-hover)] text-[var(--foreground)] border-r-2 border-teal-500"
                          : "text-[var(--foreground)]/70 hover:bg-[var(--surface-hover)] hover:text-[var(--foreground)]"
                      } transition-colors`}
                    >
                      <span className="text-lg flex-shrink-0">{item.icon}</span>
                      <span className="font-medium">{item.label}</span>
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="p-4 mt-4 border-t border-[var(--foreground)]/10">
              <button
                onClick={() => {
                  logout();
                  setIsMobileMenuOpen(false);
                }}
                className="w-full px-4 py-3 rounded-md bg-red-50 text-red-700 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/40 transition-colors flex items-center justify-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                <span>Odjavi se</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-[var(--background)] p-3 sm:p-4 md:p-6">
          <div className="max-w-7xl mx-auto">{renderMainContent()}</div>
        </main>
      </div>

      {/* RegisterForm Modal */}
      {showRegisterForm && (
        <RegisterForm
          onClose={() => setShowRegisterForm(false)}
          isAdminCreating={true}
        />
      )}
    </div>
  );
}
