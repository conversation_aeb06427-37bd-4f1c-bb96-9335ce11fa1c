<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6"/>
      <stop offset="100%" style="stop-color:#2563eb"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="8" cy="8" r="7.5" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="1"/>
  
  <!-- Calendar body -->
  <rect x="4" y="5" width="8" height="7" rx="0.5" fill="#ffffff"/>
  
  <!-- Calendar header -->
  <rect x="4" y="5" width="8" height="1.5" rx="0.5" fill="#0891b2"/>
  
  <!-- Calendar binding rings -->
  <rect x="5.5" y="3.5" width="0.5" height="2" rx="0.25" fill="#64748b"/>
  <rect x="7.75" y="3.5" width="0.5" height="2" rx="0.25" fill="#64748b"/>
  <rect x="10" y="3.5" width="0.5" height="2" rx="0.25" fill="#64748b"/>
  
  <!-- Calendar grid -->
  <g stroke="#e2e8f0" stroke-width="0.25">
    <line x1="5" y1="8" x2="11" y2="8"/>
    <line x1="5" y1="9.5" x2="11" y2="9.5"/>
    <line x1="5" y1="11" x2="11" y2="11"/>
    
    <line x1="6.5" y1="7" x2="6.5" y2="11.5"/>
    <line x1="8" y1="7" x2="8" y2="11.5"/>
    <line x1="9.5" y1="7" x2="9.5" y2="11.5"/>
  </g>
  
  <!-- Highlighted appointment date -->
  <circle cx="6.75" cy="8.75" r="0.5" fill="#14b8a6"/>
  
  <!-- Other dates -->
  <circle cx="8.25" cy="8.75" r="0.15" fill="#64748b"/>
  <circle cx="9.75" cy="8.75" r="0.15" fill="#64748b"/>
  <circle cx="8.25" cy="10.25" r="0.15" fill="#64748b"/>
</svg>
