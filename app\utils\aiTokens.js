import AIUsage from "../auth/models/AIUsage";
import crypto from "crypto";

// Cene za AI modele (po 1K tokena)
const TOKEN_PRICES = {
  "gpt-4.1-nano": {
    input: 0.0001, // $0.100 per 1M tokens = $0.0001 per 1K tokens
    cached_input: 0.000025, // $0.025 per 1M tokens = $0.000025 per 1K tokens
    output: 0.0004, // $0.400 per 1M tokens = $0.0004 per 1K tokens
  },
  "gpt-4o-mini": {
    input: 0.00015, // $0.00015 per 1K input tokens
    output: 0.0006, // $0.0006 per 1K output tokens
  },
  "gpt-3.5-turbo": {
    input: 0.0015, // $0.0015 per 1K input tokens
    output: 0.002, // $0.002 per 1K output tokens
  },
};

/**
 * Računa troškove na osnovu tokena
 */
export function calculateCost(
  model,
  inputTokens,
  outputTokens,
  cachedTokens = 0
) {
  const prices = TOKEN_PRICES[model] || TOKEN_PRICES["gpt-4.1-nano"];

  let inputCost = 0;

  // Za GPT-4.1 nano, razlikuj cached i regular input tokene
  if (model === "gpt-4.1-nano" && cachedTokens > 0) {
    const regularInputTokens = Math.max(0, inputTokens - cachedTokens);
    inputCost =
      (regularInputTokens / 1000) * prices.input +
      (cachedTokens / 1000) * prices.cached_input;
  } else {
    inputCost = (inputTokens / 1000) * prices.input;
  }

  const outputCost = (outputTokens / 1000) * prices.output;

  return inputCost + outputCost;
}

/**
 * Beleži AI usage u bazu
 */
export async function trackAIUsage(
  userId,
  feature,
  tokensUsed,
  model = "gpt-4.1-nano",
  requestData = {}
) {
  try {
    const cost = calculateCost(
      model,
      tokensUsed.prompt_tokens || 0,
      tokensUsed.completion_tokens || 0,
      tokensUsed.prompt_tokens_details?.cached_tokens || 0
    );

    const usage = new AIUsage({
      userId,
      feature,
      tokensUsed:
        (tokensUsed.prompt_tokens || 0) + (tokensUsed.completion_tokens || 0),
      model,
      cost,
      requestData,
    });

    await usage.save();
    return usage;
  } catch (error) {
    console.error("Error tracking AI usage:", error);
    return null;
  }
}

/**
 * Dobija AI usage statistike za korisnika
 */
export async function getAIUsageStats(userId, days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const usage = await AIUsage.find({
      userId,
      createdAt: { $gte: startDate },
    }).sort({ createdAt: -1 });

    const stats = {
      totalTokens: usage.reduce((sum, u) => sum + u.tokensUsed, 0),
      totalCost: usage.reduce((sum, u) => sum + u.cost, 0),
      requestCount: usage.length,
      byFeature: {},
      byDay: {},
    };

    // Grupisanje po feature-u
    usage.forEach((u) => {
      if (!stats.byFeature[u.feature]) {
        stats.byFeature[u.feature] = {
          tokens: 0,
          cost: 0,
          requests: 0,
        };
      }
      stats.byFeature[u.feature].tokens += u.tokensUsed;
      stats.byFeature[u.feature].cost += u.cost;
      stats.byFeature[u.feature].requests += 1;
    });

    // Grupisanje po danima
    usage.forEach((u) => {
      const day = u.createdAt.toISOString().split("T")[0];
      if (!stats.byDay[day]) {
        stats.byDay[day] = {
          tokens: 0,
          cost: 0,
          requests: 0,
        };
      }
      stats.byDay[day].tokens += u.tokensUsed;
      stats.byDay[day].cost += u.cost;
      stats.byDay[day].requests += 1;
    });

    return stats;
  } catch (error) {
    console.error("Error getting AI usage stats:", error);
    return null;
  }
}

/**
 * Kreira hash od podataka za cache proveru
 */
export function createDataHash(data) {
  return crypto.createHash("md5").update(JSON.stringify(data)).digest("hex");
}
