// utils/planLimits.js

export const PLAN_LIMITS = {
  standard: {
    dailyLimit: 8,
    monthlyLimit: 240,
    name: "Standard",
    price: "2.000 RSD",
  },
  "standard-plus": {
    dailyLimit: 15,
    monthlyLimit: 450,
    name: "Standard Plus",
    price: "2.800 RSD",
  },
  premium: {
    dailyLimit: null, // unlimited
    monthlyLimit: null, // unlimited
    name: "Premium",
    price: "4.500 RSD",
  },
  enterprise: {
    dailyLimit: null, // unlimited
    monthlyLimit: null, // unlimited
    name: "Enterprise",
    price: "Po proceni",
  },
};

export function getPlanLimits(planType) {
  return PLAN_LIMITS[planType] || PLAN_LIMITS.standard;
}

export function canCreateBooking(planType, monthlyCount) {
  const limits = getPlanLimits(planType);

  // Check only monthly limit (daily limit is just informative)
  if (limits.monthlyLimit && monthlyCount >= limits.monthlyLimit) {
    return {
      allowed: false,
      reason: `<PERSON><PERSON><PERSON><PERSON> ste me<PERSON>čni limit od ${limits.monthlyLimit} termina za ${limits.name} plan.`,
    };
  }

  return { allowed: true };
}
