import { NextResponse } from "next/server";
import { getRateLimitStatus, clearRateLimit } from "../../../middleware/redisRateLimit";

// GET - Dobija status rate limitinga
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const ip = searchParams.get('ip');
    const endpoint = searchParams.get('endpoint');

    if (!ip || !endpoint) {
      return NextResponse.json(
        { error: "IP adresa i endpoint su obavezni" },
        { status: 400 }
      );
    }

    const status = await getRateLimitStatus(ip, endpoint);
    
    return NextResponse.json({
      success: true,
      data: {
        ip,
        endpoint,
        ...status
      }
    });

  } catch (error) {
    console.error('Error getting rate limit status:', error);
    return NextResponse.json(
      { error: "Greška pri dobijanju rate limit statusa" },
      { status: 500 }
    );
  }
}

// DELETE - Briše rate limit za određenu IP adresu i endpoint
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const ip = searchParams.get('ip');
    const endpoint = searchParams.get('endpoint');

    if (!ip || !endpoint) {
      return NextResponse.json(
        { error: "IP adresa i endpoint su obavezni" },
        { status: 400 }
      );
    }

    const success = await clearRateLimit(ip, endpoint);
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: `Rate limit obrisan za IP: ${ip}, endpoint: ${endpoint}`
      });
    } else {
      return NextResponse.json(
        { error: "Greška pri brisanju rate limit-a" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error clearing rate limit:', error);
    return NextResponse.json(
      { error: "Greška pri brisanju rate limit-a" },
      { status: 500 }
    );
  }
}
