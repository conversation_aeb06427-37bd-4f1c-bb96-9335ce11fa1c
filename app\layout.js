import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "./contexts/ThemeProvider";
import { AuthProvider } from "./contexts/AuthProvider";
import { Suspense } from "react";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Zakaži AI | Online platforma za zakazivanje termina",
  description:
    "Pojednostavite proces zakazivanja termina uz Zakaži AI. Savršeno za salone, spa centre i uslužne delatnosti.",
  keywords:
    "zakazivanje termina, online zakazivanje, salon zakazivanje,frizerzakazivanje, spa zakazivanje, softver za upravljanje poslovanjem",
  verification: {
    google: "y49ob3BkPt_HMuum8nQoYQsRYuOtQkacnpz_C_Tgvv4",
  },
  icons: {
    icon: [
      {
        url: "/favicon.svg",
        type: "image/svg+xml",
      },
    ],
    apple: [
      {
        url: "/apple-touch-icon.svg",
        sizes: "180x180",
        type: "image/svg+xml",
      },
    ],
    shortcut: "/favicon.svg",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="sr" className="scroll-smooth" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[var(--background)] text-[var(--foreground)]`}
      >
        <Suspense
          fallback={
            <p className="min-h-screen bg-[var(--background)] flex items-center justify-center">
              Učitavanje...
            </p>
          }
        >
          <AuthProvider>
            <ThemeProvider>{children}</ThemeProvider>
          </AuthProvider>
        </Suspense>
      </body>
    </html>
  );
}
