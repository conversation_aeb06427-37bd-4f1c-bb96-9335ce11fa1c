"use client";

import { useState } from "react";
import DemoForm from "./DemoForm";

function Cena() {
  const [showDemoForm, setShowDemoForm] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);

  const handlePlanClick = (planName) => (e) => {
    e.preventDefault();
    setSelectedPlan(planName);
    setShowDemoForm(true);
  };
  return (
    <section id="cene" className="py-20 sm:py-28 bg-[var(--muted)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-extrabold text-[var(--foreground)] sm:text-5xl bg-clip-text bg-gradient-to-r from-teal-500 to-blue-600 inline-block">
            <PERSON>ne
          </h2>
          <p className="mt-6 text-xl text-[var(--foreground)]/70 max-w-3xl mx-auto leading-relaxed">
            Izaberite plan koji najbolje odgovara potrebama Vašeg salona.{" "}
            <span className="font-semibold text-teal-600 dark:text-teal-400">
              Sve planove možete prilagoditi
            </span>{" "}
            prema vašim specifičnim zahtevima.
          </p>
        </div>

        <div className="relative ">
          {/* Booking-related decorative elements */}

          <div className="hidden lg:block absolute -bottom-10 -right-70 w-28 h-28 opacity-40 animate-float animation-delay-2000">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-blue-400"
            >
              <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" />
            </svg>
          </div>
          <div className="hidden lg:block absolute top-1/2 -right-30 w-24 h-24 opacity-40 animate-float animation-delay-1000">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-purple-400"
            >
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
            </svg>
          </div>
          <div className="hidden lg:block absolute top-1/4 -left-30 w-20 h-20 opacity-40 animate-float animation-delay-3000">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-green-400"
            >
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
            </svg>
          </div>

          <div className="relative bg-[var(--surface)] rounded-2xl shadow-xl overflow-hidden border border-black/5 dark:border-white/10">
            {/* Header section */}
            <div className="px-8 py-12 border-b border-black/5 dark:border-white/10 bg-[var(--surface)]">
              <div className="flex flex-col md:flex-row items-center justify-between">
                <div className="mb-8 md:mb-0 text-center md:text-left">
                  <h3 className="text-3xl font-bold text-[var(--foreground)]">
                    Planovi prilagođeni vašim potrebama
                  </h3>
                  <p className="mt-3 text-lg text-[var(--foreground)]/70 max-w-2xl">
                    Od malih salona do velikih lanaca - imamo rešenje za
                    svakoga. Bez skrivenih troškova, transparentno i
                    jednostavno.
                  </p>
                </div>
              </div>
            </div>

            {/* Plan Cards */}
            <div className="px-8 py-12 bg-[var(--background)]">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {/* Standard Plan */}
                <div className="bg-[var(--surface)] backdrop-blur-sm rounded-2xl shadow-xl border border-black/5 dark:border-white/10 p-8 transform transition-all hover:scale-[1.02] hover:shadow-2xl flex flex-col h-full">
                  <div className="text-center mb-6">
                    <h4 className="text-2xl font-bold text-[var(--foreground)] mb-3">
                      Standard
                    </h4>
                    <div className="text-4xl font-extrabold bg-clip-text bg-gradient-to-r from-teal-500 to-blue-600 text-transparent mb-2">
                      2.400 RSD
                    </div>
                    <div className="text-sm text-[var(--foreground)]/60">
                      mesečno
                    </div>
                  </div>
                  <ul className="space-y-4 mb-8 flex-grow">
                    <li className="flex items-start">
                      <span className="text-teal-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        AI provera dostupnosti
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-teal-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Do 240 termina mesečno
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-teal-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Automatsko slanje linka
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-teal-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Jednostavna instalacija
                      </span>
                    </li>
                  </ul>
                  <button
                    onClick={handlePlanClick("Standard")}
                    className="w-full inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl text-white bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 shadow-lg hover:shadow-xl transform transition-all hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 mt-auto cursor-pointer"
                  >
                    Izaberi Standard plan
                  </button>
                </div>

                {/* Standard Plus Plan */}
                <div className="bg-[var(--surface)] backdrop-blur-sm rounded-2xl shadow-xl border-2 border-blue-500/50 p-8 transform transition-all hover:scale-[1.02] hover:shadow-2xl relative flex flex-col h-full">
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full text-xs font-bold shadow-lg">
                      Najpopularniji
                    </span>
                  </div>
                  <div className="text-center mb-6">
                    <h4 className="text-2xl font-bold text-[var(--foreground)] mb-3">
                      Standard Plus
                    </h4>
                    <div className="text-4xl font-extrabold bg-clip-text bg-gradient-to-r from-blue-500 to-blue-600 text-transparent mb-2">
                      3.000 RSD
                    </div>
                    <div className="text-sm text-[var(--foreground)]/60">
                      mesečno
                    </div>
                  </div>
                  <ul className="space-y-4 mb-8 flex-grow">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Sve iz Standard plana
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Do 450 termina mesečno
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        AI analitika
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Napredne statistike
                      </span>
                    </li>
                  </ul>
                  <button
                    onClick={handlePlanClick("Standard Plus")}
                    className="w-full inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transform transition-all hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mt-auto"
                  >
                    Izaberi Standard Plus plan
                  </button>
                </div>

                {/* Premium Plan */}
                <div className="bg-[var(--surface)] backdrop-blur-sm rounded-2xl shadow-xl border-2 border-purple-500/50 p-8 transform transition-all hover:scale-[1.02] hover:shadow-2xl flex flex-col h-full relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-2xl"></div>
                  <div className="relative text-center mb-6">
                    <h4 className="text-2xl font-bold text-[var(--foreground)] mb-3">
                      Premium
                    </h4>
                    <div className="text-4xl font-extrabold bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 text-transparent mb-2">
                      4.500 RSD
                    </div>
                    <div className="text-sm text-[var(--foreground)]/60">
                      mesečno
                    </div>
                  </div>
                  <ul className="space-y-4 mb-8 flex-grow relative">
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Neograničeno termina
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Premium podrška
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        AI komunikacija sa klijentima
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-3 text-lg">✓</span>
                      <span className="text-sm text-[var(--foreground)]/80">
                        Pamćenje istorije klijenta
                      </span>
                    </li>
                  </ul>
                  <button
                    onClick={handlePlanClick("Premium")}
                    className="w-full inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl transform transition-all hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 mt-auto relative"
                  >
                    Izaberi Premium plan
                  </button>
                </div>

                {/* Enterprise Plan */}
                <div className="bg-gradient-to-br from-gray-900/90 to-black/90 backdrop-blur-sm rounded-2xl shadow-2xl border-2 border-yellow-400/50 p-8 transform transition-all hover:scale-[1.02] hover:shadow-2xl relative flex flex-col h-full">
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-2 rounded-full text-xs font-bold shadow-lg">
                      Enterprise
                    </span>
                  </div>
                  <div className="text-center mb-6">
                    <h4 className="text-2xl font-bold text-white mb-3">
                      Enterprise
                    </h4>
                    <div className="text-3xl font-extrabold bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500 text-transparent mb-2">
                      Po proceni
                    </div>
                    <div className="text-sm text-gray-300">prilagođeno</div>
                  </div>
                  <ul className="space-y-4 mb-8 flex-grow">
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-3 text-lg">✓</span>
                      <span className="text-sm text-white">
                        Sve iz Premium paketa
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-3 text-lg">✓</span>
                      <span className="text-sm text-white">
                        Za salone sa više radnika i lokacija
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-3 text-lg">✓</span>
                      <span className="text-sm text-white">
                        Napredna izveštavanja i analiza
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-2">✓</span>
                      <span className="text-sm text-white">
                        Integracija s drugim sistemima
                      </span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-3 text-lg">✓</span>
                      <span className="text-sm text-white">
                        Prilagođene usluge
                      </span>
                    </li>
                  </ul>
                  <button
                    onClick={handlePlanClick("Enterprise")}
                    className="w-full inline-flex items-center justify-center px-6 py-3 text-base font-bold rounded-xl text-black bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 shadow-lg hover:shadow-xl transform transition-all hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 mt-auto cursor-pointer"
                  >
                    Kontaktirajte nas za Enterprise
                  </button>
                </div>
              </div>

              {/* Features comparison */}
              <div className="mt-16 grid md:grid-cols-2 gap-8">
                <div className="bg-[var(--surface)] backdrop-blur-sm p-6 rounded-xl shadow-sm border border-black/5 dark:border-white/10">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-gradient-to-br from-teal-500/10 to-teal-500/20 p-3 rounded-lg">
                      <svg
                        className="h-6 w-6 text-teal-600 dark:text-teal-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-semibold text-[var(--foreground)]">
                        Bez ugovorne obaveze
                      </h4>
                      <p className="mt-2 text-[var(--foreground)]/70">
                        Možete otkazati pretplatu u bilo kom trenutku, bez
                        dodatnih troškova
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-[var(--surface)] backdrop-blur-sm p-6 rounded-xl shadow-sm border border-black/5 dark:border-white/10">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-gradient-to-br from-blue-500/10 to-blue-500/20 p-3 rounded-lg">
                      <svg
                        className="h-6 w-6 text-blue-600 dark:text-blue-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-semibold text-[var(--foreground)]">
                        24/7 tehnička podrška
                      </h4>
                      <p className="mt-2 text-[var(--foreground)]/70">
                        Naš tim je uvek dostupan za pomoć i rešavanje problema
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* CTA Button */}
              <div className="mt-16 text-center">
                <button
                  onClick={handlePlanClick("Prilagođena ponuda")}
                  className="inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-full text-white bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 shadow-2xl hover:shadow-xl transform transition-all hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 cursor-pointer"
                >
                  Zatražite prilagođenu ponudu
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 ml-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showDemoForm && (
        <DemoForm
          onClose={() => {
            setShowDemoForm(false);
            setSelectedPlan(null);
          }}
          selectedPlan={selectedPlan}
        />
      )}
    </section>
  );
}

export default Cena;
