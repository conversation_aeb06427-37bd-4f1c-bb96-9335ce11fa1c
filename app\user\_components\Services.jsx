"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/contexts/AuthProvider";

export default function Services({ user }) {
  const { refreshUserData } = useAuth();
  const [services, setServices] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    durationMins: 30,
    price: "",
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ text: "", type: "" });

  // Funkcija za učitavanje usluga direktno sa servera
  const loadServices = async () => {
    try {
      const userId = user.user?._id || user._id;
      if (!userId) return;

      const response = await fetch(`/api/services/get-all?userId=${userId}`);

      if (!response.ok) {
        throw new Error("Greška pri učitavanju usluga");
      }

      const data = await response.json();
      setServices(data.services || []);
    } catch (err) {
      console.error("Greška pri učitavanju usluga:", err);
      // Kao fallback koristimo usluge iz user objekta
      if (user && (user.services || user.user?.services)) {
        setServices(user.user?.services || user.services || []);
      }
    }
  };

  // Dobijanje usluga iz korisničkog objekta i sa servera
  useEffect(() => {
    if (user) {
      loadServices();
    }
  }, [user]);

  // Resetovanje forme
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      durationMins: 30,
      price: "",
    });
    setEditingService(null);
  };

  // Otvaranje forme za uređivanje postojeće usluge
  const handleEditService = (service) => {
    setEditingService(service);
    setFormData({
      name: service.name || "",
      description: service.description || "",
      durationMins: service.durationMins || 30,
      price: service.price || "",
    });
    setShowForm(true);
  };

  // Otvaranje forme za dodavanje nove usluge
  const handleAddNewService = () => {
    resetForm();
    setShowForm(true);
  };

  // Brisanje usluge
  const handleDeleteService = async (serviceId) => {
    if (!confirm("Da li ste sigurni da želite da obrišete ovu uslugu?")) {
      return;
    }

    setLoading(true);
    setMessage({ text: "", type: "" });

    try {
      const userId = user.user?._id || user._id;
      const response = await fetch("/api/services/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          serviceId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška pri brisanju usluge");
      }

      // Učitaj svežu listu usluga sa servera
      await loadServices();
      setMessage({
        text: "Usluga je uspešno obrisana",
        type: "success",
      });
    } catch (err) {
      console.error("Greška pri brisanju usluge:", err);
      setMessage({
        text: err.message || "Došlo je do greške pri brisanju usluge",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Podnošenje forme za dodavanje/uređivanje usluge
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage({ text: "", type: "" });

    try {
      const userId = user.user?._id || user._id;
      const endpoint = editingService
        ? "/api/services/update"
        : "/api/services/create";

      const requestData = {
        userId,
        ...formData,
      };

      // Ako uređujemo postojeću uslugu, dodaj njen ID
      if (editingService) {
        requestData.serviceId = editingService._id;
      }

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška pri čuvanju usluge");
      }

      // Učitaj svežu listu usluga sa servera umesto lokalne manipulacije
      await loadServices();

      setMessage({
        text: `Usluga uspešno ${editingService ? "ažurirana" : "dodata"}`,
        type: "success",
      });

      // Resetovanje forme nakon uspešnog dodavanja/ažuriranja
      resetForm();
      setShowForm(false);
    } catch (err) {
      console.error("Greška pri čuvanju usluge:", err);
      setMessage({
        text: err.message || "Došlo je do greške pri čuvanju usluge",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "durationMins" ? parseInt(value, 10) : value,
    }));
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Usluge
      </h2>
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <p className="text-[var(--foreground)]/70 mb-6">
          Ovde možete upravljati uslugama koje nudite klijentima.
        </p>

        {message.text && (
          <div
            className={`p-3 mb-4 rounded-md ${
              message.type === "success"
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            }`}
          >
            {message.text}
          </div>
        )}

        <div className="flex justify-end mb-4">
          <button
            onClick={handleAddNewService}
            className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
            disabled={loading}
          >
            {loading ? "Učitavanje..." : "Dodaj novu uslugu"}
          </button>
        </div>

        {showForm && (
          <div className="mb-6 p-4 bg-[var(--background)] rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-[var(--foreground)]">
              {editingService ? "Uredi uslugu" : "Nova usluga"}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
                  Naziv usluge
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
                  placeholder="npr. Muško šišanje, Manikir..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
                  Opis usluge
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)] min-h-[100px]"
                  placeholder="Kratki opis usluge..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
                  Trajanje (minuti)
                </label>
                <input
                  type="number"
                  name="durationMins"
                  value={formData.durationMins}
                  onChange={handleChange}
                  required
                  min={5}
                  className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
                  Cena (RSD)
                </label>
                <input
                  type="text"
                  name="price"
                  value={formData.price}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
                  placeholder="npr. 1200"
                />
              </div>

              <div className="flex justify-end space-x-2 pt-2">
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    setShowForm(false);
                  }}
                  className="px-4 py-2 border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
                  disabled={loading}
                >
                  Otkaži
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
                  disabled={loading}
                >
                  {loading
                    ? "Čuvanje..."
                    : editingService
                    ? "Sačuvaj izmene"
                    : "Dodaj uslugu"}
                </button>
              </div>
            </form>
          </div>
        )}

        {services.length === 0 ? (
          <div className="bg-[var(--background)] p-4 rounded-lg text-center">
            <p className="text-[var(--foreground)]/60">
              Nemate nijednu uslugu.
            </p>
            <p className="text-[var(--foreground)]/60">
              Dodajte vašu prvu uslugu koristeći dugme iznad.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-[var(--background)] rounded-lg overflow-hidden">
              <thead className="bg-[var(--surface)]">
                <tr>
                  <th className="py-3 px-4 text-left text-sm font-medium text-[var(--foreground)]/70">
                    Naziv
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-[var(--foreground)]/70">
                    Trajanje
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-[var(--foreground)]/70">
                    Cena
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-[var(--foreground)]/70">
                    Akcije
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-[var(--border)]">
                {services.map((service) => (
                  <tr
                    key={service._id}
                    className="hover:bg-[var(--surface)]/10"
                  >
                    <td className="py-3 px-4 text-[var(--foreground)]">
                      <div className="font-medium">{service.name}</div>
                      {service.description && (
                        <div className="text-sm text-[var(--foreground)]/60 truncate max-w-[250px]">
                          {service.description}
                        </div>
                      )}
                    </td>
                    <td className="py-3 px-4 text-[var(--foreground)]">
                      {service.durationMins} min
                    </td>
                    <td className="py-3 px-4 text-[var(--foreground)]">
                      {service.price ? `${service.price} RSD` : "Cena na upit"}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditService(service)}
                          className="px-3 py-1 text-sm bg-[var(--surface)] hover:bg-[var(--surface-hover)] text-[var(--foreground)] rounded transition-colors"
                        >
                          Uredi
                        </button>
                        <button
                          onClick={() => handleDeleteService(service._id)}
                          className="px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors"
                        >
                          Obriši
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
