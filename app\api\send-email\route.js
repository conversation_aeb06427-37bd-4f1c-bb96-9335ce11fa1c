import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import Request from "../../auth/models/Request";

export async function POST(request) {
  try {
    await dbConnect();

    const body = await request.json();
    const {
      name,
      email,
      phone,
      businessType,
      businessName,
      message,
      type = "free-trial",
      preferredTime,
    } = body;

    // Validacija podataka
    if (!name || !email || !phone || !businessType || !businessName) {
      return NextResponse.json(
        { error: "Sva obavezna polja moraju biti popunjena" },
        { status: 400 }
      );
    }

    // Kreiranje novog zahteva u MongoDB
    const newRequest = new Request({
      name,
      email,
      phone,
      businessType,
      businessName,
      message: message || "",
      type,
      preferredTime: preferredTime || "",
      status: "novo",
    });

    // Čuvanje u MongoDB
    const savedRequest = await newRequest.save();

    console.log("New request saved to MongoDB:", savedRequest);

    // Simuliramo delay za realističnost
    await new Promise((resolve) => setTimeout(resolve, 500));

    const isDemo = type === "demo";
    return NextResponse.json(
      {
        success: true,
        message: isDemo
          ? "Zahtev za demo je uspešno poslat. Kontaktiraćemo vas u najkraćem roku!"
          : "Zahtev za besplatni mesec je uspešno poslat. Kontaktiraćemo vas u najkraćem roku!",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error saving request:", error);
    return NextResponse.json(
      {
        error:
          "Došlo je do greške prilikom slanja zahteva. Molimo pokušajte ponovo.",
      },
      { status: 500 }
    );
  }
}

// Dodajemo i GET metodu za testiranje
export async function GET() {
  return NextResponse.json({ message: "Email API is working" });
}
