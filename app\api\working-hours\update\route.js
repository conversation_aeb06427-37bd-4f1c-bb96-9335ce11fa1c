// app/api/working-hours/update/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";
export async function POST(request) {
  await dbConnect();

  const body = await request.json();
  const { userId, workingHours } = body;

  // Validacija podataka
  if (!userId || !workingHours) {
    return NextResponse.json(
      { error: "Nedostaju obavezni podaci" },
      { status: 400 }
    );
  }

  try {
    // Pronađi korisnika
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Validacija radnog vremena
    const requiredDays = [
      "ponedeljak",
      "utorak",
      "sreda",
      "cetvrtak",
      "petak",
      "subota",
      "nedelja",
    ];
    const providedDays = Object.keys(workingHours);

    const missingDays = requiredDays.filter(
      (day) => !providedDays.includes(day)
    );
    if (missingDays.length > 0) {
      return NextResponse.json(
        {
          error: `Nedostaju dani u radnom vremenu: ${missingDays.join(", ")}`,
        },
        { status: 400 }
      );
    }

    // Validacija formata radnog vremena za svaki dan
    for (const [day, hours] of Object.entries(workingHours)) {
      if (typeof hours !== "object") {
        return NextResponse.json(
          {
            error: `Nevažeći format radnog vremena za dan: ${day}`,
          },
          { status: 400 }
        );
      }

      // Provera da li sadrži closed polje
      if (typeof hours.closed !== "boolean") {
        return NextResponse.json(
          {
            error: `Nevažeća vrednost za 'closed' polje za dan: ${day}`,
          },
          { status: 400 }
        );
      }

      // Provera from i to polja ako dan nije zatvoren
      if (!hours.closed) {
        if (!hours.from || !hours.to) {
          return NextResponse.json(
            {
              error: `Nedostaju 'from' ili 'to' polja za dan: ${day}`,
            },
            { status: 400 }
          );
        }

        // Validacija formata vremena (HH:MM)
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(hours.from) || !timeRegex.test(hours.to)) {
          return NextResponse.json(
            {
              error: `Nevažeći format vremena za dan: ${day}. Mora biti u formatu HH:MM`,
            },
            { status: 400 }
          );
        }
      }

      // Validacija pauza ako postoje
      if (hours.breaks && Array.isArray(hours.breaks)) {
        for (let i = 0; i < hours.breaks.length; i++) {
          const breakTime = hours.breaks[i];
          if (!breakTime.from || !breakTime.to) {
            return NextResponse.json(
              {
                error: `Nedostaju 'from' ili 'to' polja za pauzu ${
                  i + 1
                } za dan: ${day}`,
              },
              { status: 400 }
            );
          }

          // Validacija formata vremena za pauze
          const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
          if (
            !timeRegex.test(breakTime.from) ||
            !timeRegex.test(breakTime.to)
          ) {
            return NextResponse.json(
              {
                error: `Nevažeći format vremena za pauzu ${
                  i + 1
                } za dan: ${day}. Mora biti u formatu HH:MM`,
              },
              { status: 400 }
            );
          }
        }
      }
    }

    // Ažuriraj radno vreme korisnika
    user.workingHours = workingHours;
    user.updatedAt = new Date();

    // Sačuvaj promene
    await user.save();

    return NextResponse.json({
      success: true,
      message: "Radno vreme je uspešno ažurirano",
    });
  } catch (error) {
    console.error("Error updating working hours:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
