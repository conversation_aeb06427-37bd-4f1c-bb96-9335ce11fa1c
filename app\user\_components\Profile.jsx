"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/app/contexts/AuthProvider";

export default function Profile({ user }) {
  const { refreshUserData } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.user.name || "",
    email: user?.user.email || "",
    phone: user?.user.phone || "",
    businessName: user?.user.businessName || "",
    businessType: user?.user.businessType || "",
    urlSlug: user?.user.urlSlug || "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ text: "", type: "" });

  // Update form data when user prop changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user?.user.name || "",
        email: user?.user.email || "",
        phone: user?.user.phone || "",
        businessName: user?.user.businessName || "",
        businessType: user?.user.businessType || "",
        urlSlug: user?.user.urlSlug || "",
      });
    }
  }, [user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage({ text: "", type: "" });

    try {
      const response = await fetch("/api/update-user", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user?.user._id,
          userData: formData,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({
          text: "Profil je uspešno ažuriran",
          type: "success",
        });
        setIsEditing(false);

        // Refresh user data in context instead of reloading the page
        await refreshUserData();
      } else {
        setMessage({
          text: data.error || "Došlo je do greške prilikom ažuriranja profila",
          type: "error",
        });
      }
    } catch (error) {
      setMessage({
        text: "Došlo je do greške prilikom komunikacije sa serverom",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const cancelEdit = () => {
    setFormData({
      name: user?.user.name || "",
      email: user?.user.email || "",
      phone: user?.user.phone || "",
      businessName: user?.user.businessName || "",
      businessType: user?.user.businessType || "",
      urlSlug: user?.user.urlSlug || "",
    });
    setIsEditing(false);
    setMessage({ text: "", type: "" });
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Profil
      </h2>
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        {message.text && (
          <div
            className={`mb-4 p-3 rounded-md ${
              message.type === "success"
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {message.text}
          </div>
        )}

        {isEditing ? (
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">
                  Informacije o biznisu
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-[var(--foreground)]/60 mb-1">
                      Naziv biznisa
                    </label>
                    <input
                      type="text"
                      name="businessName"
                      value={formData.businessName}
                      onChange={handleChange}
                      className="w-full p-2 border border-[var(--foreground)]/10 rounded-md bg-[var(--background)]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-[var(--foreground)]/60 mb-1">
                      Tip biznisa
                    </label>
                    <input
                      type="text"
                      name="businessType"
                      value={formData.businessType}
                      onChange={handleChange}
                      className="w-full p-2 border border-[var(--foreground)]/10 rounded-md bg-[var(--background)]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-[var(--foreground)]/60 mb-1">
                      URL identifikator
                    </label>
                    <input
                      disabled
                      type="text"
                      name="urlSlug"
                      value={formData.urlSlug}
                      onChange={handleChange}
                      className="w-full p-2 border border-[var(--foreground)]/10 rounded-md bg-[var(--background)]"
                    />
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">
                  Kontakt informacije
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-[var(--foreground)]/60 mb-1">
                      Ime
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full p-2 border border-[var(--foreground)]/10 rounded-md bg-[var(--background)]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-[var(--foreground)]/60 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full p-2 border border-[var(--foreground)]/10 rounded-md bg-[var(--background)]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-[var(--foreground)]/60 mb-1">
                      Telefon
                    </label>
                    <input
                      type="text"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full p-2 border border-[var(--foreground)]/10 rounded-md bg-[var(--background)]"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-6 flex gap-3">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-[var(--foreground)] text-[var(--background)] rounded-md hover:opacity-90 transition-opacity disabled:opacity-70"
              >
                {isSubmitting ? "Sačuvaj..." : "Sačuvaj promene"}
              </button>
              <button
                type="button"
                onClick={cancelEdit}
                className="px-4 py-2 bg-[var(--background)] text-[var(--foreground)] border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
              >
                Otkaži
              </button>
            </div>
          </form>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">
                  Informacije o biznisu
                </h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-[var(--foreground)]/60">
                      Naziv biznisa
                    </p>
                    <p className="font-medium">{user?.user.businessName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[var(--foreground)]/60">
                      Tip biznisa
                    </p>
                    <p className="font-medium">
                      {user?.user.businessType || "Nije definisano"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-[var(--foreground)]/60">
                      URL identifikator
                    </p>
                    <p className="font-medium">{user?.user.urlSlug}</p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">
                  Kontakt informacije
                </h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-[var(--foreground)]/60">Ime</p>
                    <p className="font-medium">{user?.user.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[var(--foreground)]/60">Email</p>
                    <p className="font-medium">{user?.user.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[var(--foreground)]/60">
                      Telefon
                    </p>
                    <p className="font-medium">{user?.user.phone}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-6">
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-[var(--background)] text-[var(--foreground)] border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
              >
                Izmeni profil
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
