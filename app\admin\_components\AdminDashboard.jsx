"use client";

import React, { useState, useEffect } from "react";

export default function AdminDashboard({ user, setActiveSection }) {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBookings: 0,
    totalRequests: 0,
    recentActivity: []
  });
  const [isLoading, setIsLoading] = useState(true);

  const loadAdminStats = async () => {
    try {
      setIsLoading(true);
      
      // Učitaj osnovne statistike
      const [usersResponse, requestsResponse] = await Promise.all([
        fetch("/api/admin/users"),
        fetch("/api/admin/requests")
      ]);

      const usersData = await usersResponse.json();
      const requestsData = await requestsResponse.json();

      setStats({
        totalUsers: usersData.count || 0,
        totalBookings: 0, // Možemo dodati endpoint za ovo kasnije
        totalRequests: Array.isArray(requestsData) ? requestsData.length : 0,
        recentActivity: Array.isArray(requestsData) ? requestsData.slice(0, 5) : []
      });

    } catch (error) {
      console.error("Greška pri učitavanju admin statistika:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAdminStats();
  }, []);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Dobrodošli, Administrator!
      </h2>
      <p className="text-[var(--foreground)]/70 mb-6">
        Ovo je vaša admin kontrolna tabla za upravljanje sistemom.
      </p>

      {/* Admin Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-[var(--background)] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
          <h3 className="font-medium text-[var(--foreground)] mb-2">
            Ukupno korisnika
          </h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-bold text-blue-600">
              {isLoading ? "..." : stats.totalUsers}
            </p>
            <div className="ml-2 text-sm text-[var(--foreground)]/70">
              registrovanih
            </div>
          </div>
        </div>

        <div className="bg-[var(--background)] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
          <h3 className="font-medium text-[var(--foreground)] mb-2">
            Aktivni zahtevi
          </h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-bold text-orange-600">
              {isLoading ? "..." : stats.totalRequests}
            </p>
            <div className="ml-2 text-sm text-[var(--foreground)]/70">
              zahteva
            </div>
          </div>
        </div>

        <div className="bg-[var(--background)] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
          <h3 className="font-medium text-[var(--foreground)] mb-2">
            Sistem status
          </h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-bold text-green-600">
              ✓
            </p>
            <div className="ml-2 text-sm text-[var(--foreground)]/70">
              Aktivan
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h3 className="text-lg font-medium text-[var(--foreground)] mb-4">
          Brze akcije
        </h3>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => setActiveSection("requests")}
            className="px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white rounded-md transition-colors"
          >
            Upravljaj zahtevima
          </button>
          <button
            onClick={() => setActiveSection("users")}
            className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
          >
            Upravljaj korisnicima
          </button>
          <button
            onClick={() => setActiveSection("analytics")}
            className="px-4 py-2 bg-[var(--background)] text-[var(--foreground)] border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
          >
            AI Analitika
          </button>
          <button
            onClick={() => setActiveSection("cleanup")}
            className="px-4 py-2 bg-[var(--background)] text-[var(--foreground)] border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
          >
            Čišćenje sistema
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h3 className="text-lg font-medium text-[var(--foreground)] mb-4">
          Nedavna aktivnost
        </h3>
        <div className="bg-[var(--background)] rounded-lg shadow-sm">
          {isLoading ? (
            <div className="p-4 text-center text-[var(--foreground)]/70">
              Učitavanje...
            </div>
          ) : stats.recentActivity.length > 0 ? (
            <div className="divide-y divide-[var(--foreground)]/10">
              {stats.recentActivity.map((activity, index) => (
                <div key={activity.id || index} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-[var(--foreground)]">
                        {activity.name}
                      </p>
                      <p className="text-sm text-[var(--foreground)]/70">
                        {activity.type === "plan-change" 
                          ? `Zahtev za promenu plana: ${activity.currentPlan} → ${activity.requestedPlan}`
                          : activity.type === "demo" 
                          ? "Demo zahtev"
                          : "Zahtev za besplatni mesec"
                        }
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        activity.status === "novo" 
                          ? "bg-blue-100 text-blue-800" 
                          : activity.status === "kontaktirano"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-green-100 text-green-800"
                      }`}>
                        {activity.status}
                      </span>
                      <p className="text-xs text-[var(--foreground)]/50 mt-1">
                        {new Date(activity.createdAt).toLocaleDateString("sr-RS")}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-[var(--foreground)]/70">
              Nema nedavne aktivnosti
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
