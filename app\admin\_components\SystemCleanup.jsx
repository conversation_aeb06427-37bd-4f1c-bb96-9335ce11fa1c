"use client";

import React, { useState, useEffect } from "react";

export default function SystemCleanup() {
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [cleanupMessage, setCleanupMessage] = useState({ text: "", type: "" });
  const [oldBookingsCount, setOldBookingsCount] = useState(0);
  const [loading, setLoading] = useState(true);

  const checkOldBookings = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/cleanup-old-bookings");
      const data = await response.json();
      if (data.success) {
        setOldBookingsCount(data.totalOldBookings);
      }
    } catch (error) {
      console.error("Error checking old bookings:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCleanupOldBookings = async () => {
    if (
      !confirm(
        "Da li ste sigurni da želite da obrišete sve termine starije od 30 dana? Ova akcija se ne može poništiti."
      )
    ) {
      return;
    }

    setCleanupLoading(true);
    setCleanupMessage({ text: "", type: "" });

    try {
      const response = await fetch("/api/admin/cleanup-old-bookings", {
        method: "DELETE",
      });
      const data = await response.json();

      if (data.success) {
        setCleanupMessage({
          text: data.message,
          type: "success",
        });
        await checkOldBookings();
      } else {
        setCleanupMessage({
          text: data.error || "Greška prilikom brisanja termina",
          type: "error",
        });
      }
    } catch (error) {
      setCleanupMessage({
        text: "Greška prilikom komunikacije sa serverom",
        type: "error",
      });
    } finally {
      setCleanupLoading(false);
    }
  };

  useEffect(() => {
    checkOldBookings();
  }, []);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Čišćenje sistema
      </h2>
      <p className="text-[var(--foreground)]/70 mb-6">
        Upravljanje i čišćenje starih podataka u sistemu.
      </p>

      <div className="space-y-6">
        {/* Database Cleanup Section */}
        <div className="bg-[var(--surface)] rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-[var(--foreground)] mb-4">
            Čišćenje baze podataka
          </h3>
          
          {/* Cleanup Message */}
          {cleanupMessage.text && (
            <div
              className={`mb-4 p-4 rounded-lg ${
                cleanupMessage.type === "success"
                  ? "bg-green-100 text-green-800 border border-green-200"
                  : "bg-red-100 text-red-800 border border-red-200"
              }`}
            >
              {cleanupMessage.text}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Old Bookings Cleanup */}
            <div className="border border-[var(--foreground)]/10 rounded-lg p-4">
              <h4 className="font-medium text-[var(--foreground)] mb-2">
                Stari termini
              </h4>
              <p className="text-sm text-[var(--foreground)]/70 mb-4">
                Obriši termine starije od 30 dana iz obe kolekcije (bookings i pastBookings).
              </p>
              
              {loading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto"></div>
                  <p className="text-sm text-[var(--foreground)]/70 mt-2">Proverava se...</p>
                </div>
              ) : (
                <>
                  <div className="mb-4">
                    <div className="text-2xl font-bold text-orange-600">
                      {oldBookingsCount}
                    </div>
                    <div className="text-sm text-[var(--foreground)]/70">
                      termina za brisanje
                    </div>
                  </div>
                  
                  <button
                    onClick={handleCleanupOldBookings}
                    disabled={cleanupLoading || oldBookingsCount === 0}
                    className={`w-full px-4 py-2 rounded-md transition-colors ${
                      oldBookingsCount === 0
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : cleanupLoading
                        ? "bg-orange-400 text-white cursor-not-allowed"
                        : "bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white"
                    }`}
                  >
                    {cleanupLoading
                      ? "Briše se..."
                      : oldBookingsCount > 0
                      ? `Obriši ${oldBookingsCount} termina`
                      : "Nema starih termina"}
                  </button>
                </>
              )}
            </div>

            {/* Cache Cleanup */}
            <div className="border border-[var(--foreground)]/10 rounded-lg p-4">
              <h4 className="font-medium text-[var(--foreground)] mb-2">
                AI Analytics Cache
              </h4>
              <p className="text-sm text-[var(--foreground)]/70 mb-4">
                Obriši stari cache AI analitike (automatski se briše nakon 24h).
              </p>
              
              <div className="mb-4">
                <div className="text-2xl font-bold text-blue-600">
                  Auto
                </div>
                <div className="text-sm text-[var(--foreground)]/70">
                  čišćenje aktivno
                </div>
              </div>
              
              <button
                disabled
                className="w-full px-4 py-2 bg-gray-300 text-gray-500 cursor-not-allowed rounded-md"
              >
                Automatsko čišćenje
              </button>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="bg-[var(--surface)] rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-[var(--foreground)] mb-4">
            Informacije o sistemu
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-[var(--background)] rounded-lg">
              <div className="text-lg font-semibold text-green-600">
                Aktivan
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Status sistema
              </div>
            </div>
            
            <div className="text-center p-4 bg-[var(--background)] rounded-lg">
              <div className="text-lg font-semibold text-blue-600">
                MongoDB
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Baza podataka
              </div>
            </div>
            
            <div className="text-center p-4 bg-[var(--background)] rounded-lg">
              <div className="text-lg font-semibold text-purple-600">
                Next.js
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Framework
              </div>
            </div>
          </div>
        </div>

        {/* Maintenance Tips */}
        <div className="bg-[var(--surface)] rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-[var(--foreground)] mb-4">
            Preporuke za održavanje
          </h3>
          
          <div className="space-y-3 text-sm text-[var(--foreground)]/70">
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-0.5">✓</span>
              <span>Redovno brisanje starih termina (mesečno)</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-0.5">✓</span>
              <span>AI Analytics cache se automatski čisti nakon 24h</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-0.5">✓</span>
              <span>Monitoring AI usage statistika za optimizaciju troškova</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-yellow-500 mt-0.5">!</span>
              <span>Backup baze podataka se preporučuje pre većih čišćenja</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
