import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import Request from "../../../auth/models/Request";
import PlanChangeRequest from "../../../auth/models/PlanChangeRequest";

// GET - Dobijanje svih zahteva
export async function GET() {
  try {
    await dbConnect();

    // Dobijanje svih zahteva iz MongoDB, sortirani po datumu (najnoviji prvi)
    const requests = await Request.find({}).sort({ timestamp: -1 });
    const planChangeRequests = await PlanChangeRequest.find({}).sort({
      timestamp: -1,
    });

    // Kombinovanje oba tipa zahteva
    const allRequests = [...requests, ...planChangeRequests].sort(
      (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
    );

    return NextResponse.json(allRequests);
  } catch (error) {
    console.error("Error reading requests:", error);
    return NextResponse.json(
      { error: "Greška prilikom čitanja zahteva" },
      { status: 500 }
    );
  }
}

// PUT - Ažuriranje statusa zahteva
export async function PUT(request) {
  try {
    await dbConnect();

    const { id, status } = await request.json();

    // Pokušaj ažuriranje u Request kolekciji
    let updatedRequest = await Request.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );

    // Ako nije pronađen u Request, pokušaj u PlanChangeRequest
    if (!updatedRequest) {
      updatedRequest = await PlanChangeRequest.findByIdAndUpdate(
        id,
        { status },
        { new: true }
      );
    }

    if (!updatedRequest) {
      return NextResponse.json(
        { error: "Zahtev nije pronađen" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Status zahteva je uspešno ažuriran",
      request: updatedRequest,
    });
  } catch (error) {
    console.error("Error updating request:", error);
    return NextResponse.json(
      { error: "Greška prilikom ažuriranja zahteva" },
      { status: 500 }
    );
  }
}

// DELETE - Brisanje zahteva
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "ID zahteva je obavezan" },
        { status: 400 }
      );
    }

    const dataPath = path.join(process.cwd(), "data", "requests.json");

    if (!fs.existsSync(dataPath)) {
      return NextResponse.json(
        { error: "Fajl sa zahtevima ne postoji" },
        { status: 404 }
      );
    }

    const fileContent = fs.readFileSync(dataPath, "utf8");
    let requests = JSON.parse(fileContent);

    // Filtriranje zahteva (uklanjanje onog sa datim ID)
    const filteredRequests = requests.filter((req) => req.id !== id);

    if (filteredRequests.length === requests.length) {
      return NextResponse.json(
        { error: "Zahtev nije pronađen" },
        { status: 404 }
      );
    }

    // Čuvanje ažuriranih podataka
    fs.writeFileSync(dataPath, JSON.stringify(filteredRequests, null, 2));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting request:", error);
    return NextResponse.json(
      { error: "Greška prilikom brisanja zahteva" },
      { status: 500 }
    );
  }
}
