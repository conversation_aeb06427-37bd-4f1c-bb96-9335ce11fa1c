import { NextResponse } from "next/server";

// Test endpoint za testiranje cron job funkcionalnosti
export async function POST(request) {
  try {
    const cronSecret = process.env.CRON_SECRET;

    // Pozovi refresh endpoint
    const refreshResponse = await fetch(
      `${process.env.NEXTAUTH_URL}/api/ai/analytics/refresh`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ cronSecret }),
      }
    );

    const result = await refreshResponse.json();

    return NextResponse.json({
      success: true,
      message: "Test refresh completed",
      result,
    });
  } catch (error) {
    console.error("Test refresh error:", error);
    return NextResponse.json(
      { error: "Greška pri test refresh-u", details: error.message },
      { status: 500 }
    );
  }
}

// GET endpoint za brzu proveru
export async function GET() {
  return NextResponse.json({
    message: "AI Analytics Auto Refresh Test Endpoint",
    cronSchedule: "Svakog ponedeljka u 09:00 (0 9 * * 1)",
    status: "ready",
  });
}
