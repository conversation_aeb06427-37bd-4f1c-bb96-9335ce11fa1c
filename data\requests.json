[{"id": "1751062636449", "userId": "685f182e6205c3f116961164", "name": "Salon stanrard", "email": "<EMAIL>", "businessName": "Salon test standard", "currentPlan": "standard", "requestedPlan": "standard-plus", "type": "plan-change", "createdAt": "2025-06-27T22:17:16.449Z", "status": "novo"}, {"id": "1751061965941", "userId": "6848117262d3f4e1feff9c0f", "name": "test 3 3", "email": "<EMAIL>", "businessName": "Salon test 3", "currentPlan": "premium", "requestedPlan": "enterprise", "type": "plan-change", "createdAt": "2025-06-27T22:06:05.941Z", "status": "novo"}, {"id": "1751061814299", "userId": "6848117262d3f4e1feff9c0f", "name": "test 3 3", "email": "<EMAIL>", "businessName": "Salon test 3", "currentPlan": "premium", "requestedPlan": "standard-plus", "type": "plan-change", "createdAt": "2025-06-27T22:03:34.299Z", "status": "novo"}, {"id": "1750109590101", "name": "demo test", "email": "demo@test", "phone": "+381695338913", "businessType": "massage", "businessName": "demo test", "message": "", "type": "demo", "preferredTime": "morning", "createdAt": "2025-06-16T21:33:10.101Z", "status": "novo"}, {"id": "1750109290267", "name": "test", "email": "<EMAIL>", "phone": "+381695338913", "businessType": "salon", "businessName": "test", "message": "", "type": "free-trial", "preferredTime": "", "createdAt": "2025-06-16T21:28:10.267Z", "status": "<PERSON>av<PERSON>š<PERSON>", "updatedAt": "2025-06-16T21:29:25.439Z"}]