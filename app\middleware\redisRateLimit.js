import Redis from 'ioredis';

// Redis konfiguracija
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
});

// Fallback in-memory store ako Redis nije dostupan
const memoryStore = new Map();

// Proverava da li je Redis dostupan
let redisAvailable = false;
redis.ping()
  .then(() => {
    redisAvailable = true;
    console.log('✅ Redis connected for rate limiting');
  })
  .catch(() => {
    redisAvailable = false;
    console.log('⚠️ Redis not available, using in-memory rate limiting');
  });

// Redis rate limiting funkcija
async function redisRateLimit(key, max, windowMs) {
  try {
    const multi = redis.multi();
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const redisKey = `rate_limit:${key}:${window}`;

    multi.incr(redisKey);
    multi.expire(redisKey, Math.ceil(windowMs / 1000));
    
    const results = await multi.exec();
    const count = results[0][1];

    return {
      allowed: count <= max,
      count: count,
      remaining: Math.max(0, max - count),
      resetTime: (window + 1) * windowMs,
      retryAfter: count > max ? Math.ceil(windowMs / 1000) : 0
    };
  } catch (error) {
    console.error('Redis rate limit error:', error);
    // Fallback to memory store
    return memoryRateLimit(key, max, windowMs);
  }
}

// Memory rate limiting funkcija (fallback)
function memoryRateLimit(key, max, windowMs) {
  const now = Date.now();
  const window = Math.floor(now / windowMs);
  const memoryKey = `${key}:${window}`;

  // Čisti stare unose
  for (const [k, data] of memoryStore.entries()) {
    if (now > data.resetTime) {
      memoryStore.delete(k);
    }
  }

  if (!memoryStore.has(memoryKey)) {
    memoryStore.set(memoryKey, {
      count: 1,
      resetTime: (window + 1) * windowMs
    });
    return {
      allowed: true,
      count: 1,
      remaining: max - 1,
      resetTime: (window + 1) * windowMs,
      retryAfter: 0
    };
  }

  const data = memoryStore.get(memoryKey);
  data.count++;
  memoryStore.set(memoryKey, data);

  return {
    allowed: data.count <= max,
    count: data.count,
    remaining: Math.max(0, max - data.count),
    resetTime: data.resetTime,
    retryAfter: data.count > max ? Math.ceil(windowMs / 1000) : 0
  };
}

// Glavna rate limiting funkcija
export async function advancedRateLimit(ip, endpoint, options = {}) {
  const {
    max = 100,
    windowMs = 15 * 60 * 1000, // 15 minuta
    keyGenerator = (ip, endpoint) => `${ip}:${endpoint}`
  } = options;

  const key = keyGenerator(ip, endpoint);

  if (redisAvailable) {
    return await redisRateLimit(key, max, windowMs);
  } else {
    return memoryRateLimit(key, max, windowMs);
  }
}

// Specifične rate limiting funkcije
export async function loginRateLimit(ip, email) {
  return await advancedRateLimit(ip, 'login', {
    max: 5,
    windowMs: 15 * 60 * 1000,
    keyGenerator: (ip) => `login:${ip}:${email}`
  });
}

export async function registerRateLimit(ip) {
  return await advancedRateLimit(ip, 'register', {
    max: 3,
    windowMs: 60 * 60 * 1000
  });
}

export async function emailRateLimit(ip) {
  return await advancedRateLimit(ip, 'email', {
    max: 10,
    windowMs: 60 * 60 * 1000
  });
}

export async function bookingRateLimit(ip) {
  return await advancedRateLimit(ip, 'booking', {
    max: 10,
    windowMs: 60 * 1000
  });
}

export async function aiRateLimit(ip) {
  return await advancedRateLimit(ip, 'ai', {
    max: 20,
    windowMs: 60 * 1000
  });
}

// Funkcija za brisanje rate limit podataka (admin funkcija)
export async function clearRateLimit(ip, endpoint) {
  try {
    if (redisAvailable) {
      const pattern = `rate_limit:${ip}:${endpoint}:*`;
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } else {
      // Briše iz memory store
      for (const key of memoryStore.keys()) {
        if (key.startsWith(`${ip}:${endpoint}`)) {
          memoryStore.delete(key);
        }
      }
    }
    return true;
  } catch (error) {
    console.error('Error clearing rate limit:', error);
    return false;
  }
}

// Funkcija za dobijanje trenutnog stanja rate limit-a
export async function getRateLimitStatus(ip, endpoint) {
  try {
    const key = `${ip}:${endpoint}`;
    
    if (redisAvailable) {
      const now = Date.now();
      const window = Math.floor(now / (15 * 60 * 1000)); // Default 15 min window
      const redisKey = `rate_limit:${key}:${window}`;
      const count = await redis.get(redisKey) || 0;
      const ttl = await redis.ttl(redisKey);
      
      return {
        count: parseInt(count),
        resetTime: ttl > 0 ? Date.now() + (ttl * 1000) : null
      };
    } else {
      // Memory store status
      for (const [k, data] of memoryStore.entries()) {
        if (k.startsWith(key)) {
          return {
            count: data.count,
            resetTime: data.resetTime
          };
        }
      }
      return { count: 0, resetTime: null };
    }
  } catch (error) {
    console.error('Error getting rate limit status:', error);
    return { count: 0, resetTime: null };
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  redis.disconnect();
});

process.on('SIGINT', () => {
  redis.disconnect();
});
