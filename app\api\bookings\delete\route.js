// app/api/bookings/delete/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import Booking from "../../../auth/models/Booking";

export async function DELETE(request) {
  await dbConnect();

  // Parse the JSON body
  const body = await request.json();
  const { userId, bookingId } = body;

  // Validacija podataka
  if (!userId || !bookingId) {
    return NextResponse.json(
      { error: "Nedostaju potrebni podaci" },
      { status: 400 }
    );
  }

  try {
    // Pronađi i obriši zakazivanje direktno iz Booking kolekcije
    const result = await Booking.findOneAndDelete({
      _id: bookingId,
      userId: userId,
    });

    if (!result) {
      return NextResponse.json(
        { error: "Zakazivanje nije pronađeno" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Rezervacija je uspešno obrisana",
    });
  } catch (error) {
    console.error("Error deleting booking:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
