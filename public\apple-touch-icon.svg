<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6"/>
      <stop offset="100%" style="stop-color:#2563eb"/>
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners for iOS -->
  <rect width="180" height="180" rx="40" fill="url(#bgGradient)"/>
  
  <!-- Calendar body -->
  <rect x="45" y="55" width="90" height="80" rx="8" fill="#ffffff"/>
  
  <!-- Calendar header -->
  <rect x="45" y="55" width="90" height="20" rx="8" fill="#0891b2"/>
  
  <!-- Calendar binding rings -->
  <rect x="65" y="35" width="6" height="25" rx="3" fill="#64748b"/>
  <rect x="87" y="35" width="6" height="25" rx="3" fill="#64748b"/>
  <rect x="109" y="35" width="6" height="25" rx="3" fill="#64748b"/>
  
  <!-- Calendar grid -->
  <g stroke="#e2e8f0" stroke-width="2">
    <line x1="55" y1="90" x2="125" y2="90"/>
    <line x1="55" y1="105" x2="125" y2="105"/>
    <line x1="55" y1="120" x2="125" y2="120"/>
    
    <line x1="70" y1="80" x2="70" y2="130"/>
    <line x1="85" y1="80" x2="85" y2="130"/>
    <line x1="100" y1="80" x2="100" y2="130"/>
    <line x1="115" y1="80" x2="115" y2="130"/>
  </g>
  
  <!-- Highlighted appointment date -->
  <circle cx="77.5" cy="97.5" r="6" fill="#14b8a6"/>
  
  <!-- Other dates -->
  <circle cx="92.5" cy="97.5" r="2" fill="#64748b"/>
  <circle cx="107.5" cy="97.5" r="2" fill="#64748b"/>
  <circle cx="62.5" cy="112.5" r="2" fill="#64748b"/>
  <circle cx="92.5" cy="112.5" r="2" fill="#64748b"/>
  <circle cx="107.5" cy="112.5" r="2" fill="#64748b"/>
  <circle cx="77.5" cy="127.5" r="2" fill="#64748b"/>
  <circle cx="107.5" cy="127.5" r="2" fill="#64748b"/>
</svg>
