"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import FreeTrialForm from "./FreeTrialForm";
import DemoForm from "./DemoForm";

function Hero() {
  const [scrollY, setScrollY] = useState(0);
  const [showFreeTrialForm, setShowFreeTrialForm] = useState(false);
  const [showDemoForm, setShowDemoForm] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleFreeTrialClick = (e) => {
    e.preventDefault();
    setShowFreeTrialForm(true);
  };

  const handleDemoClick = (e) => {
    e.preventDefault();
    setShowDemoForm(true);
  };
  return (
    <main className="flex-grow">
      {/* Hero Section */}
      <section className="relative h-screen overflow-hidden bg-[var(--muted)] py-20 sm:py-32">
        {/* Animated booking-related background elements */}
        <div className="absolute inset-0 z-0 hidden sm:block">
          {/* Calendar/Schedule Icon */}
          <div className="absolute top-20 left-4 sm:left-10 w-20 h-20 sm:w-32 sm:h-32 opacity-30 sm:opacity-40 animate-float">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-teal-500"
            >
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
            </svg>
          </div>

          {/* Phone/Call Icon */}
          <div className="absolute top-40 right-4 sm:right-10 w-16 h-16 sm:w-28 sm:h-28 opacity-30 sm:opacity-40 animate-float animation-delay-2000">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-blue-500"
            >
              <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
            </svg>
          </div>

          {/* Clock/Time Icon */}
          <div className="absolute bottom-20 left-20 sm:left-40 w-24 h-24 sm:w-36 sm:h-36 opacity-30 sm:opacity-40 animate-float animation-delay-4000">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-purple-500"
            >
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z" />
            </svg>
          </div>

          {/* Appointment/User Icon */}
          <div className="absolute bottom-40 right-10 sm:right-20 w-16 h-16 sm:w-20 sm:h-20 opacity-30 sm:opacity-40 animate-float animation-delay-3000">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-green-500"
            >
              <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
            </svg>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="lg:grid lg:grid-cols-12 lg:gap-8">
            <div className="text-center sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left animate-fade-in-up">
              <h1 className="text-3xl tracking-tight font-extrabold text-[var(--foreground)] sm:text-4xl md:text-5xl lg:text-6xl">
                <span className="block animate-fade-in-up animation-delay-200">
                  Pojednostavite zakazivanje{" "}
                </span>{" "}
                <span className="block bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-blue-600 animate-fade-in-up animation-delay-600 min-h-[1.2em]">
                  sa Zakaži AI
                </span>
              </h1>
              <p className="mt-6 text-base text-[var(--foreground)]/70 sm:text-lg md:text-xl animate-fade-in-up animation-delay-600">
                Transformišite proces zakazivanja termina uz našu platformu
                zasnovanu na veštačkoj inteligenciji. Savršeno za salone, spa
                centre i uslužne delatnosti.
              </p>
              <div className="mt-10 flex flex-col sm:flex-row sm:justify-center lg:justify-start gap-3 animate-fade-in-up animation-delay-800">
                <div
                  className="rounded-md shadow-lg hover:shadow-2xl transition-all duration-300"
                  style={{ pointerEvents: "auto" }}
                >
                  <button
                    onClick={handleFreeTrialClick}
                    className="group w-full flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 sm:px-8 md:py-4 md:text-base lg:text-lg lg:px-10 transition-all duration-300 cursor-pointer relative z-20 transform hover:scale-105 hover:-translate-y-1"
                  >
                    <span className="relative font-bold z-10 whitespace-nowrap">
                      Započnite besplatno
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-blue-700 rounded-md opacity-0 group-hover:opacity-400 transition-opacity duration-300"></div>
                  </button>
                </div>
                <div
                  className="rounded-md shadow-lg hover:shadow-2xl transition-all duration-300"
                  style={{ pointerEvents: "auto" }}
                >
                  <button
                    onClick={handleDemoClick}
                    className="group w-full flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-[var(--foreground)] bg-[var(--surface)] hover:bg-[var(--surface-hover)] sm:px-8 md:py-4 md:text-base lg:text-lg lg:px-10 transition-all duration-300 cursor-pointer relative z-20 transform hover:scale-105 hover:-translate-y-1"
                  >
                    <span className="relative z-10 whitespace-nowrap">
                      Zakažite demo
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <div className="mt-12 relative sm:max-w-lg sm:mx-auto lg:mt-0 lg:max-w-none lg:mx-0 lg:col-span-6 lg:flex lg:items-center animate-fade-in-right animation-delay-1000">
              <div className="relative mx-auto w-full rounded-lg shadow-2xl lg:max-w-md transform hover:scale-105 transition-all duration-500 animate-float">
                <div className="relative block w-full bg-[var(--surface)] rounded-lg overflow-hidden border border-white/10">
                  <div className="aspect-w-16 aspect-h-9">
                    <Image
                      priority={true}
                      src="/dashboard-preview.svg"
                      alt="Zakaži AI pregled kontrolne table"
                      width={800}
                      height={450}
                      className="rounded-lg transition-transform duration-500 hover:scale-110"
                    />
                  </div>
                  <div className="absolute inset-0 w-full h-full flex items-center justify-center group cursor-pointer">
                    <div className="relative">
                      <svg
                        className="h-20 w-20 text-white transform group-hover:scale-110 transition-transform duration-300 animate-pulse-slow"
                        fill="currentColor"
                        viewBox="0 0 84 84"
                      >
                        <circle
                          opacity="0.9"
                          cx="42"
                          cy="42"
                          r="42"
                          fill="white"
                          className="drop-shadow-lg"
                        />
                        <path d="M55 42L35 55V29L55 42Z" fill="#0284c7" />
                      </svg>
                      <div className="absolute inset-0 rounded-full bg-white/20 animate-ping"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {showFreeTrialForm && (
        <FreeTrialForm onClose={() => setShowFreeTrialForm(false)} />
      )}

      {showDemoForm && <DemoForm onClose={() => setShowDemoForm(false)} />}
    </main>
  );
}

export default Hero;
