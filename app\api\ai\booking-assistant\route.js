import { NextResponse } from "next/server";
import OpenAI from "openai";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";
import Booking from "../../../auth/models/Booking";
import { trackAIUsage } from "../../../utils/aiTokens";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Funkcija za pronalaženje sledećeg dostupnog termina
const findNextAvailableSlot = (
  user,
  requestedDate,
  requestedTime,
  existingBookings,
  serviceDuration = 30
) => {
  const dayMapping = [
    "nedelja",
    "ponedeljak",
    "utorak",
    "sreda",
    "cetvrtak",
    "petak",
    "subota",
  ];

  // Počni od traženog datuma
  let currentDate = new Date(requestedDate);

  // Traži do 14 dana unapred
  for (let i = 0; i < 14; i++) {
    const dayOfWeek = currentDate.getDay();
    const dayKey = dayMapping[dayOfWeek];
    const daySchedule = user.workingHours?.[dayKey];

    if (!daySchedule || daySchedule.closed) {
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    // Generiši vremenske slotove za ovaj dan
    const timeSlots = generateTimeSlotsForDay(daySchedule, serviceDuration);

    // Ako je prvi dan (traženi datum), filtriraj slotove da budu posle traženog vremena
    let filteredTimeSlots = timeSlots;
    if (i === 0 && requestedTime) {
      // Konvertuj traženo vreme u minute za lakše poređenje
      const [reqHour, reqMinute] = requestedTime.split(":").map(Number);
      const requestedMinutes = reqHour * 60 + reqMinute;

      filteredTimeSlots = timeSlots.filter((timeSlot) => {
        const [slotHour, slotMinute] = timeSlot.split(":").map(Number);
        const slotMinutes = slotHour * 60 + slotMinute;
        return slotMinutes >= requestedMinutes; // Samo slotovi posle traženog vremena
      });
    }

    // Proveri dostupnost za svaki slot
    for (const timeSlot of filteredTimeSlots) {
      const slotStart = new Date(
        `${currentDate.toISOString().split("T")[0]}T${timeSlot}`
      );
      const slotEnd = new Date(slotStart.getTime() + serviceDuration * 60000);

      // Proveri da li se preklapa sa postojećim booking-ima
      const hasConflict = existingBookings.some((booking) => {
        if (!booking.date || !booking.time) return false;

        // Kreiraj start i end vreme na osnovu date i time
        const bookingStart = new Date(booking.date);
        // Pretpostavljamo 30 minuta trajanje ako nije specificirano
        const bookingEnd = new Date(bookingStart.getTime() + 30 * 60000);

        return slotStart < bookingEnd && slotEnd > bookingStart;
      });

      if (!hasConflict) {
        return {
          date: currentDate.toISOString().split("T")[0],
          time: timeSlot,
          dayName: dayKey,
        };
      }
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return null;
};

// Funkcija za generisanje vremenskih slotova
const generateTimeSlotsForDay = (daySchedule, serviceDuration = 30) => {
  if (
    !daySchedule ||
    daySchedule.closed ||
    !daySchedule.from ||
    !daySchedule.to
  ) {
    return [];
  }

  const slots = [];
  const interval = 30; // 30 minuta između termina

  try {
    const [fromHour, fromMinute] = daySchedule.from.split(":").map(Number);
    const [toHour, toMinute] = daySchedule.to.split(":").map(Number);

    if (
      isNaN(fromHour) ||
      isNaN(fromMinute) ||
      isNaN(toHour) ||
      isNaN(toMinute)
    ) {
      return [];
    }

    const fromMinutes = fromHour * 60 + fromMinute;
    const toMinutes = toHour * 60 + toMinute;

    for (let mins = fromMinutes; mins < toMinutes; mins += interval) {
      const hour = Math.floor(mins / 60);
      const minute = mins % 60;
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;

      // Proveri da li je vreme u pauzi
      let isInBreak = false;
      if (daySchedule.breaks && Array.isArray(daySchedule.breaks)) {
        isInBreak = daySchedule.breaks.some((breakTime) => {
          if (!breakTime || !breakTime.from || !breakTime.to) return false;

          try {
            const [breakFromHour, breakFromMinute] = breakTime.from
              .split(":")
              .map(Number);
            const [breakToHour, breakToMinute] = breakTime.to
              .split(":")
              .map(Number);

            if (
              isNaN(breakFromHour) ||
              isNaN(breakFromMinute) ||
              isNaN(breakToHour) ||
              isNaN(breakToMinute)
            ) {
              return false;
            }

            const breakFromMinutes = breakFromHour * 60 + breakFromMinute;
            const breakToMinutes = breakToHour * 60 + breakToMinute;

            return mins >= breakFromMinutes && mins < breakToMinutes;
          } catch (err) {
            return false;
          }
        });
      }

      if (!isInBreak) {
        slots.push(timeStr);
      }
    }

    return slots;
  } catch (err) {
    return [];
  }
};

// Funkcija za proveru dostupnosti termina
const checkTimeSlotAvailability = (
  user,
  date,
  time,
  existingBookings,
  serviceDuration = 30
) => {
  const dayMapping = [
    "nedelja",
    "ponedeljak",
    "utorak",
    "sreda",
    "cetvrtak",
    "petak",
    "subota",
  ];
  const requestedDate = new Date(date);
  const dayOfWeek = requestedDate.getDay();
  const dayKey = dayMapping[dayOfWeek];

  // Proveri radno vreme
  const daySchedule = user.workingHours?.[dayKey];
  if (!daySchedule || daySchedule.closed) {
    return { available: false, reason: "Salon je zatvoren tog dana" };
  }

  // Proveri da li je vreme u radnom vremenu
  const timeSlots = generateTimeSlotsForDay(daySchedule, serviceDuration);
  if (!timeSlots.includes(time)) {
    return { available: false, reason: "Vreme nije u radnom vremenu salona" };
  }

  // Proveri preklapanja sa postojećim booking-ima
  const slotStart = new Date(`${date}T${time}`);
  const slotEnd = new Date(slotStart.getTime() + serviceDuration * 60000);

  const hasConflict = existingBookings.some((booking) => {
    if (!booking.date || !booking.time) return false;

    // Kreiraj start i end vreme na osnovu date i time
    const bookingStart = new Date(booking.date);
    // Pretpostavljamo 30 minuta trajanje ako nije specificirano
    const bookingEnd = new Date(bookingStart.getTime() + 30 * 60000);

    return slotStart < bookingEnd && slotEnd > bookingStart;
  });

  if (hasConflict) {
    return { available: false, reason: "Termin je već zauzet" };
  }

  return { available: true };
};

export async function POST(request) {
  try {
    const { message, userId, context } = await request.json();

    if (!message || !userId) {
      return NextResponse.json(
        { error: "Poruka i userId su obavezni" },
        { status: 400 }
      );
    }

    // Povezivanje sa bazom
    await dbConnect();

    // Dohvatanje korisnika (salon)
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Dohvatanje postojećih zakazivanja za proveru dostupnosti
    const existingBookings = await Booking.find({
      userId,
      date: { $gte: new Date() }, // Samo buduća zakazivanja
    });

    // Formatiranje postojećih zakazivanja za lakše čitanje
    const formattedBookings = existingBookings
      .filter((b) => b.date && b.time)
      .map((b) => {
        const date = new Date(b.date);
        const dateStr = date.toLocaleDateString("sr-RS");
        return `${dateStr} ${b.time} - ${b.clientName} (${b.status})`;
      })
      .join("\n");

    // Formatiranje radnog vremena
    const workingHoursText = Object.entries(user.workingHours || {})
      .map(([day, schedule]) => {
        if (schedule.closed) return `${day}: zatvoreno`;
        const breaksText =
          schedule.breaks && schedule.breaks.length > 0
            ? ` (pauze: ${schedule.breaks
                .map((b) => `${b.from}-${b.to}`)
                .join(", ")})`
            : "";
        return `${day}: ${schedule.from || "9:00"} - ${
          schedule.to || "17:00"
        }${breaksText}`;
      })
      .join("\n");

    // Parsiranje poruke za izvlačenje podataka
    const extractBookingInfo = (message) => {
      const info = {};

      // Ako je poruka iz hibridne forme, možda već imamo podatke o klijentu
      if (context?.clientInfo) {
        info.name = context.clientInfo.name;
        info.phone = context.clientInfo.phone;
        info.serviceId = context.clientInfo.serviceId;
        info.service = context.clientInfo.serviceName;
        info.serviceDuration = context.clientInfo.serviceDuration || 30;
        if (context.clientInfo.requestedDate) {
          info.date = context.clientInfo.requestedDate;
        }
      } else {
        // Izvlačenje imena
        const nameMatch = message.match(
          /(?:zovem se|ime mi je|ja sam)\s+([a-zA-ZšđčćžŠĐČĆŽ\s]+)/i
        );
        if (nameMatch) info.name = nameMatch[1].trim();

        // Izvlačenje telefona
        const phoneMatch = message.match(
          /(?:telefon|broj|pozovite me na)\s*:?\s*(\d{9,10})/i
        );
        if (phoneMatch) info.phone = phoneMatch[1];

        // Izvlačenje usluge
        const services = user.services || [];
        for (const service of services) {
          if (message.toLowerCase().includes(service.name.toLowerCase())) {
            info.service = service.name;
            info.serviceId = service._id;
            info.serviceDuration = service.durationMins || 30;
            break;
          }
        }
      }

      // Izvlačenje datuma - različiti formati
      let dateMatch = message.match(/(\d{1,2})[.\-\/](\d{1,2})[.\-\/](\d{4})/);
      if (dateMatch) {
        const [, day, month, year] = dateMatch;
        info.date = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
      } else {
        // Relativni datumi
        const today = new Date();
        if (message.toLowerCase().includes("sutra")) {
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          info.date = tomorrow.toISOString().split("T")[0];
        } else if (message.toLowerCase().includes("prekosutra")) {
          const dayAfterTomorrow = new Date(today);
          dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
          info.date = dayAfterTomorrow.toISOString().split("T")[0];
        } else if (message.toLowerCase().includes("danas")) {
          info.date = today.toISOString().split("T")[0];
        }
      }

      // Izvlačenje vremena
      const timeMatch = message.match(
        /(?:u|oko)\s*(\d{1,2})[:\.]?(\d{0,2})\s*(?:h|sati)?/i
      );
      if (timeMatch) {
        const [, hour, minute = "00"] = timeMatch;
        info.time = `${hour.padStart(2, "0")}:${minute.padStart(2, "0")}`;
      }

      return info;
    };

    const bookingInfo = extractBookingInfo(message);

    // Proveri dostupnost ako imamo datum i vreme
    let availabilityCheck = null;
    let nextAvailable = null;

    if (bookingInfo.date && bookingInfo.time) {
      availabilityCheck = checkTimeSlotAvailability(
        user,
        bookingInfo.date,
        bookingInfo.time,
        existingBookings,
        bookingInfo.serviceDuration || 30
      );

      if (!availabilityCheck.available) {
        nextAvailable = findNextAvailableSlot(
          user,
          bookingInfo.date,
          bookingInfo.time,
          existingBookings,
          bookingInfo.serviceDuration || 30
        );
      }
    }

    // Kreiranje konteksta za AI
    const systemPrompt = `
Ti si AI asistent za zakazivanje termina u salonu "${user.name}".

INFORMACIJE O SALONU:
- Naziv: ${user.name}
- Usluge: ${
      user.services
        ?.map((s) => `${s.name} (${s.durationMins} min, ${s.price} RSD)`)
        .join(", ") || "Nema definisanih usluga"
    }

RADNO VREME:
${workingHoursText}

POSTOJEĆA ZAKAZIVANJA:
${formattedBookings || "Nema postojećih zakazivanja"}

TRENUTNA ANALIZA PORUKE:
${bookingInfo.name ? `- Ime: ${bookingInfo.name}` : "- Ime: nije navedeno"}
${
  bookingInfo.phone
    ? `- Telefon: ${bookingInfo.phone}`
    : "- Telefon: nije naveden"
}
${
  bookingInfo.service
    ? `- Usluga: ${bookingInfo.service}`
    : "- Usluga: nije navedena"
}
${bookingInfo.date ? `- Datum: ${bookingInfo.date}` : "- Datum: nije naveden"}
${bookingInfo.time ? `- Vreme: ${bookingInfo.time}` : "- Vreme: nije navedeno"}

${
  availabilityCheck
    ? availabilityCheck.available
      ? "✅ TERMIN JE DOSTUPAN!"
      : `❌ TERMIN NIJE DOSTUPAN: ${availabilityCheck.reason}`
    : ""
}

${
  nextAvailable
    ? `🔄 SLEDEĆI DOSTUPAN TERMIN: ${nextAvailable.date} u ${nextAvailable.time}`
    : ""
}

TVOJA ULOGA:
1. Analiziraj korisničku poruku i izvuci vreme termina
2. ${
      context?.clientInfo
        ? "Klijent je već uneo osnovne podatke, fokusiraj se na pronalaženje vremena"
        : "Traži sve potrebne informacije (ime, telefon, uslugu, datum, vreme)"
    }
3. Ako imaš sve podatke i termin JE DOSTUPAN, odgovori sa "TERMIN JE DOSTUPAN - ZAKAZUJTE TERMIN"
4. Ako termin NIJE DOSTUPAN, predloži sledeći dostupan termin
5. Govori srpskim jezikom, budi profesionalan i ljubazan

VAŽNE NAPOMENE:
- ${
      context?.clientInfo
        ? "Fokusiraj se samo na pronalaženje vremena za dati datum"
        : "Traži sve potrebne informacije pre potvrde"
    }
- Ako termin nije dostupan, predloži SLEDEĆI dostupan termin POSLE traženog vremena
- Npr. ako klijent traži 13:00 a to nije dostupno, predloži 13:30, 14:00, itd. - NE 10:00
- Ne potvrđuj termine koji nisu dostupni
- Budi precizan sa vremenima i objasni zašto predlažeš određeno vreme

Odgovori kratko i jasno na korisničku poruku.
`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: message },
      ],
      max_tokens: 500,
      temperature: 0.7,
    });

    const aiResponse = response.choices[0].message.content;

    // Parsiranje AI odgovora za strukturirane podatke
    let bookingData = null;

    // Ako AI kaže da je termin dostupan i imamo sve podatke
    if (
      aiResponse.includes("TERMIN JE DOSTUPAN - ZAKAZUJTE TERMIN") &&
      bookingInfo.name &&
      bookingInfo.phone &&
      bookingInfo.date &&
      bookingInfo.time &&
      bookingInfo.serviceId
    ) {
      bookingData = {
        name: bookingInfo.name,
        phone: bookingInfo.phone,
        date: bookingInfo.date,
        time: bookingInfo.time,
        serviceId: bookingInfo.serviceId,
        service: bookingInfo.service,
      };
    }

    // Tracking AI usage
    await trackAIUsage(
      userId,
      "booking-assistant",
      response.usage,
      "gpt-4o-mini",
      {
        messageLength: message.length,
        availabilityChecked: !!availabilityCheck,
        nextAvailableFound: !!nextAvailable,
      }
    );

    return NextResponse.json({
      response: aiResponse,
      bookingData,
      availabilityInfo: availabilityCheck,
      nextAvailable,
      extractedInfo: bookingInfo,
      success: true,
    });
  } catch (error) {
    console.error("AI Booking Assistant Error:", error);
    return NextResponse.json(
      { error: "Greška pri komunikaciji sa AI asistentom" },
      { status: 500 }
    );
  }
}
