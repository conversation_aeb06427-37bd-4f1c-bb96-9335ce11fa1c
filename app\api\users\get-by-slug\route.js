import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get("slug");

    if (!slug) {
      return NextResponse.json(
        { error: "Nedostaje slug parametar" },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find user by slug or urlSlug
    const user = await User.findOne({
      $or: [{ slug: slug }, { urlSlug: slug }],
    }).lean();

    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Convert MongoDB document to plain object
    const serializedUser = JSON.parse(JSON.stringify(user));

    return NextResponse.json({ user: serializedUser });
  } catch (error) {
    console.error("Error fetching user by slug:", error);
    return NextResponse.json(
      { error: "Greška pri dohvatanju korisnika" },
      { status: 500 }
    );
  }
}
