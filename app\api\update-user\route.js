// app/api/update-user/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";

export async function PUT(request) {
  const body = await request.json();
  const { userId, userData } = body;

  if (!userId || !userData) {
    return NextResponse.json(
      { error: "Nedostaju userId i userData u telu zahteva" },
      { status: 400 }
    );
  }

  try {
    await dbConnect();

    // Pronađi korisnika u bazi podataka
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Ažuriraj podatke korisnika
    const updatableFields = [
      "name",
      "email",
      "phone",
      "businessName",
      "businessType",
      "urlSlug",
    ];

    updatableFields.forEach((field) => {
      if (userData[field] !== undefined) {
        user[field] = userData[field];
      }
    });

    // Sačuvaj promene
    await user.save();

    // Vrati ažurirane podatke o korisniku bez lozinke
    const { password, ...userWithoutPassword } = user.toObject();

    return NextResponse.json({
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error("Error updating user data:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
