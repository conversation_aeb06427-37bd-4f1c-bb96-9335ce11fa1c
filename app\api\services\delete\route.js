// app/api/services/delete/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";

export async function DELETE(request) {
  await dbConnect();

  const body = await request.json();
  const { userId, serviceId } = body;

  // Validacija podataka
  if (!userId || !serviceId) {
    return NextResponse.json(
      { error: "Nedostaju obavezni podaci" },
      { status: 400 }
    );
  }

  try {
    // Pronađi korisnika
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Proveri da li postoji niz usluga
    if (!user.services || !Array.isArray(user.services)) {
      return NextResponse.json(
        { error: "Usluge nisu pronađene" },
        { status: 404 }
      );
    }

    // Proveri da li usluga postoji
    const serviceIndex = user.services.findIndex(
      (service) => service._id.toString() === serviceId
    );

    if (serviceIndex === -1) {
      return NextResponse.json(
        { error: "Usluga nije pronađena" },
        { status: 404 }
      );
    }

    // Ukloni uslugu iz niza
    user.services.splice(serviceIndex, 1);

    // Sačuvaj promene
    await user.save();

    return NextResponse.json({
      success: true,
      message: "Usluga je uspešno obrisana",
    });
  } catch (error) {
    console.error("Error deleting service:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
