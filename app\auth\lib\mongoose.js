// lib/mongoose.js
// lib/mongoose.js
import mongoose from "mongoose";

let cached = global.mongoose;
if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

export default async function dbConnect() {
  if (cached.conn) {
    return cached.conn;
  }
  if (!cached.promise) {
    const uri = process.env.MONGODB_URI;
    if (!uri) {
      throw new Error("MONGODB_URI nije definisan u .env.local");
    }
    // Opcije za Mongoose
    cached.promise = mongoose
      .connect(uri, {
        bufferCommands: false,
        // useNewUrlParser: true,        // sada Next.js + mongoose ne zahteva visoke verzije
        // useUnifiedTopology: true,    // ali može da ostane ako nema gre<PERSON>
      })
      .then((mongoose) => mongoose);
  }
  cached.conn = await cached.promise;
  return cached.conn;
}
