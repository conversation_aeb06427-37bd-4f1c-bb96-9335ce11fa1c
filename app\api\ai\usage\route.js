import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import { getAIUsageStats } from "../../../utils/aiTokens";

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");
    const days = parseInt(searchParams.get("days")) || 30;

    if (!userId) {
      return NextResponse.json(
        { error: "userId je obavezan" },
        { status: 400 }
      );
    }

    // Povezivanje sa bazom
    await dbConnect();

    // Dohvatanje AI usage statistika
    const stats = await getAIUsageStats(userId, days);

    if (!stats) {
      return NextResponse.json(
        { error: "Greška pri dohvatanju statistika" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      stats,
      period: `${days} dana`,
      success: true,
    });
  } catch (error) {
    console.error("AI Usage Stats Error:", error);
    return NextResponse.json(
      { error: "Greška pri dohvatanju AI statistika" },
      { status: 500 }
    );
  }
}
