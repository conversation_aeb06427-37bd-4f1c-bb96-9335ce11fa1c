"use client";

import { useState, useEffect } from "react";

export default function AIUsageStats() {
  const [allUsageStats, setAllUsageStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState("30days");
  const [error, setError] = useState("");
  const [selectedUser, setSelectedUser] = useState("all");
  const [users, setUsers] = useState([]);

  const loadAllUsers = async () => {
    try {
      const response = await fetch("/api/admin/users");
      const data = await response.json();

      if (response.ok) {
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error("Greška pri učitavanju korisnika:", error);
    }
  };

  const loadAIUsageStats = async () => {
    setLoading(true);
    setError("");

    try {
      let url = "/api/admin/ai-usage-stats";
      const params = new URLSearchParams({
        days: dateRange === "7days" ? "7" : "30",
      });

      if (selectedUser !== "all") {
        params.append("userId", selectedUser);
      }

      url += "?" + params.toString();

      const response = await fetch(url);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška pri učitavanju AI statistika");
      }

      setAllUsageStats(data);
    } catch (error) {
      console.error("Greška pri AI usage statistikama:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllUsers();
  }, []);

  useEffect(() => {
    loadAIUsageStats();
  }, [dateRange, selectedUser]);

  const handleDateRangeChange = (newDateRange) => {
    setDateRange(newDateRange);
  };

  const handleUserChange = (userId) => {
    setSelectedUser(userId);
  };

  return (
    <div className="space-y-6">
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <div className="flex items-center mb-4">
          <div className="text-3xl mr-3">💰</div>
          <div>
            <h3 className="text-xl font-semibold text-[var(--foreground)]">
              AI Potrošnja - Administratorski pregled
            </h3>
            <p className="text-[var(--foreground)]/70">
              Pregled potrošnje AI tokena i troškova za sve korisnike
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-[var(--foreground)]/70">
                Korisnik:
              </span>
              <select
                value={selectedUser}
                onChange={(e) => handleUserChange(e.target.value)}
                className="px-3 py-1 rounded border bg-[var(--background)] text-[var(--foreground)]"
              >
                <option value="all">Svi korisnici</option>
                {users.map((user) => (
                  <option key={user._id} value={user._id}>
                    {user.name} ({user.email})
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-[var(--foreground)]/70">Period:</span>
            <button
              onClick={() => handleDateRangeChange("7days")}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                dateRange === "7days"
                  ? "bg-blue-500 text-white"
                  : "bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--surface-hover)]"
              }`}
            >
              7 dana
            </button>
            <button
              onClick={() => handleDateRangeChange("30days")}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                dateRange === "30days"
                  ? "bg-blue-500 text-white"
                  : "bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--surface-hover)]"
              }`}
            >
              30 dana
            </button>
          </div>
        </div>

        {/* Error */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-[var(--foreground)]/70">
              Učitavanje AI statistika...
            </span>
          </div>
        )}

        {/* Overall Stats */}
        {allUsageStats && !loading && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                <div className="text-2xl font-bold text-purple-600">
                  {allUsageStats.totalStats?.totalTokens?.toLocaleString() || 0}
                </div>
                <div className="text-sm text-purple-700">Ukupno tokena</div>
              </div>
              <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                <div className="text-2xl font-bold text-green-600">
                  ${(allUsageStats.totalStats?.totalCost || 0).toFixed(4)}
                </div>
                <div className="text-sm text-green-700">Ukupan trošak</div>
              </div>
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                <div className="text-2xl font-bold text-blue-600">
                  {allUsageStats.totalStats?.requestCount || 0}
                </div>
                <div className="text-sm text-blue-700">Ukupno zahteva</div>
              </div>
              <div className="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
                <div className="text-2xl font-bold text-orange-600">
                  {allUsageStats.activeUsers || 0}
                </div>
                <div className="text-sm text-orange-700">Aktivni korisnici</div>
              </div>
            </div>

            {/* Feature Breakdown */}
            {allUsageStats.totalStats?.byFeature && (
              <div className="bg-[var(--background)] p-4 rounded-lg">
                <h4 className="font-semibold text-[var(--foreground)] mb-3">
                  Podela po funkcionalnostima
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(allUsageStats.totalStats.byFeature).map(
                    ([feature, stats]) => (
                      <div key={feature} className="border rounded-lg p-3">
                        <h5 className="font-medium text-[var(--foreground)] mb-2">
                          {feature === "booking-assistant"
                            ? "🤖 AI Booking"
                            : "📊 AI Analitika"}
                        </h5>
                        <div className="grid grid-cols-3 gap-2 text-sm">
                          <div>
                            <div className="font-semibold text-purple-600">
                              {stats.tokens?.toLocaleString() || 0}
                            </div>
                            <div className="text-[var(--foreground)]/60">
                              Tokeni
                            </div>
                          </div>
                          <div>
                            <div className="font-semibold text-green-600">
                              ${(stats.cost || 0).toFixed(4)}
                            </div>
                            <div className="text-[var(--foreground)]/60">
                              Trošak
                            </div>
                          </div>
                          <div>
                            <div className="font-semibold text-blue-600">
                              {stats.requests || 0}
                            </div>
                            <div className="text-[var(--foreground)]/60">
                              Zahtevi
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

            {/* User Breakdown (if showing all users) */}
            {selectedUser === "all" && allUsageStats.userStats && (
              <div className="bg-[var(--background)] p-4 rounded-lg">
                <h4 className="font-semibold text-[var(--foreground)] mb-3">
                  Podela po korisnicima (Top 10)
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Korisnik</th>
                        <th className="text-right p-2">Tokeni</th>
                        <th className="text-right p-2">Trošak</th>
                        <th className="text-right p-2">Zahtevi</th>
                        <th className="text-right p-2">Plan</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allUsageStats.userStats.slice(0, 10).map((userStat) => (
                        <tr key={userStat.userId} className="border-b">
                          <td className="p-2">
                            <div>
                              <div className="font-medium">
                                {userStat.userName}
                              </div>
                              <div className="text-[var(--foreground)]/60 text-xs">
                                {userStat.userEmail}
                              </div>
                            </div>
                          </td>
                          <td className="text-right p-2 font-mono">
                            {userStat.totalTokens.toLocaleString()}
                          </td>
                          <td className="text-right p-2 font-mono">
                            ${userStat.totalCost.toFixed(4)}
                          </td>
                          <td className="text-right p-2">
                            {userStat.requestCount}
                          </td>
                          <td className="text-right p-2">
                            <span
                              className={`px-2 py-1 rounded text-xs ${
                                userStat.userPlan === "premium"
                                  ? "bg-purple-100 text-purple-800"
                                  : userStat.userPlan === "standard-plus"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {userStat.userPlan}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Daily Breakdown */}
            {allUsageStats.totalStats?.byDay && (
              <div className="bg-[var(--background)] p-4 rounded-lg">
                <h4 className="font-semibold text-[var(--foreground)] mb-3">
                  Dnevna potrošnja (poslednih 7 dana)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-7 gap-2">
                  {Object.entries(allUsageStats.totalStats.byDay)
                    .slice(-7)
                    .map(([date, stats]) => (
                      <div
                        key={date}
                        className="border rounded p-2 text-center"
                      >
                        <div className="text-xs text-[var(--foreground)]/60 mb-1">
                          {new Date(date).toLocaleDateString("sr-RS", {
                            month: "short",
                            day: "numeric",
                          })}
                        </div>
                        <div className="text-sm font-semibold text-purple-600">
                          {stats.tokens?.toLocaleString() || 0}
                        </div>
                        <div className="text-xs text-green-600">
                          ${(stats.cost || 0).toFixed(3)}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Refresh Button */}
        <div className="mt-6 flex justify-center">
          <button
            onClick={loadAIUsageStats}
            disabled={loading}
            className="px-6 py-2 bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white rounded-md transition-colors disabled:opacity-50"
          >
            {loading ? "Učitava..." : "Osvežiti statistike"}
          </button>
        </div>
      </div>

      {/* Info */}
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <h4 className="font-semibold text-[var(--foreground)] mb-3 flex items-center">
          <span className="text-2xl mr-2">ℹ️</span>
          Informacije o AI potrošnji
        </h4>
        <div className="space-y-2 text-sm text-[var(--foreground)]/70">
          <p>
            • <strong>Model:</strong> GPT-4.1 nano ($0.100 input, $0.025 cached
            input, $0.400 output per 1M tokena)
          </p>
          <p>
            • <strong>Booking Assistant:</strong> Dostupan svim korisnicima
          </p>
          <p>
            • <strong>AI Analitika:</strong> Dostupna Standard Plus planu i više
          </p>
          <p>
            • <strong>Cache:</strong> AI analitika se čuva 24 sata za
            optimizaciju troškova
          </p>
          <p>
            • <strong>Cached tokeni:</strong> Ponovni zahtevi koriste cached
            tokene (75% jeftiniji)
          </p>
          <p>
            • <strong>Praćenje:</strong> Svi AI zahtevi se automatski prate i
            beležе
          </p>
        </div>
      </div>
    </div>
  );
}
