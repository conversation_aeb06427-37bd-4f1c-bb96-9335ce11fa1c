"use client";

import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/app/contexts/AuthProvider";

export default function HybridBookingForm({ user, services, onComplete }) {
  const { refreshUserData } = useAuth();

  // Osnovna forma podaci
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    serviceId: "",
    date: "",
    time: "",
  });

  // AI chat za termine
  const [messages, setMessages] = useState([
    {
      role: "assistant",
      content: `Zdravo! 👋 Ja sam AI asistent za pronalaženje slobodnih termina.

Recite mi u koje vreme biste želeli da dođete i ja ću proveriti dostupnost.
`,
    },
  ]);

  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedDateTime, setSelectedDateTime] = useState(null);
  const messagesEndRef = useRef(null);

  // Update serviceId when services are loaded
  useEffect(() => {
    if (services && services.length > 0 && !formData.serviceId) {
      setFormData((prev) => ({
        ...prev,
        serviceId: services[0]._id,
      }));
    }
  }, [services]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // ESC key handler za zatvaranje modal-a
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === "Escape" && showConfirmation) {
        handleCancelConfirmation();
      }
    };

    document.addEventListener("keydown", handleEscKey);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [showConfirmation]);

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    // Proveri da li su osnovni podaci uneseni
    if (
      !formData.name ||
      !formData.phone ||
      !formData.serviceId ||
      !formData.date
    ) {
      setMessages((prev) => [
        ...prev,
        { role: "user", content: inputMessage.trim() },
        {
          role: "assistant",
          content:
            "Molim vas prvo unesite vaše ime, telefon, izaberite uslugu i datum u formi iznad, a zatim ću vam pomoći da pronađem dostupno vreme. 😊",
        },
      ]);
      setInputMessage("");
      return;
    }

    const userMessage = inputMessage.trim();
    setInputMessage("");
    setIsLoading(true);

    // Dodaj korisničku poruku
    setMessages((prev) => [...prev, { role: "user", content: userMessage }]);

    try {
      const userId = user?.user?._id || user?._id;
      const selectedService = services.find(
        (s) => s._id === formData.serviceId
      );

      const response = await fetch("/api/ai/booking-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: `${userMessage}. Klijent se zove ${formData.name}, telefon ${formData.phone}, želi uslugu ${selectedService?.name} za datum ${formData.date}`,
          userId,
          context: {
            services,
            clientInfo: {
              name: formData.name,
              phone: formData.phone,
              serviceId: formData.serviceId,
              serviceName: selectedService?.name,
              serviceDuration: selectedService?.durationMins || 30,
              requestedDate: formData.date,
            },
          },
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.error || "Greška pri komunikaciji sa AI asistentom"
        );
      }

      // Dodaj AI odgovor
      let aiMessage = data.response;

      // Ako postoji sledeći dostupan termin, dodaj ga u poruku
      if (data.nextAvailable && !data.availabilityInfo?.available) {
        const nextDate = new Date(data.nextAvailable.date).toLocaleDateString(
          "sr-RS"
        );
        aiMessage += `\n\n💡 **Sledeći dostupan termin:** ${nextDate} u ${data.nextAvailable.time}h`;
      }

      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: aiMessage,
          availabilityInfo: data.availabilityInfo,
          nextAvailable: data.nextAvailable,
        },
      ]);

      // Ako AI je pronašao dostupan termin
      if (
        data.availabilityInfo?.available &&
        data.extractedInfo?.date &&
        data.extractedInfo?.time
      ) {
        setSelectedDateTime({
          date: data.extractedInfo.date,
          time: data.extractedInfo.time,
        });
        setFormData((prev) => ({
          ...prev,
          date: data.extractedInfo.date,
          time: data.extractedInfo.time,
        }));
        setShowConfirmation(true);
      }
    } catch (error) {
      console.error("Greška:", error);
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "Izvinjavam se, došlo je do greške. Molim vas pokušajte ponovo. 😔",
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickBookNext = (nextAvailable) => {
    const quickMessage = `Zakaži termin ${nextAvailable.date} u ${nextAvailable.time}`;
    setInputMessage(quickMessage);
  };

  const resetAIAgent = () => {
    setMessages([
      {
        role: "assistant",
        content: `Zdravo! 👋 Ja sam AI asistent za pronalaženje vremena termina.

Recite mi u koje vreme biste želeli da dođete i ja ću proveriti dostupnost.
`,
      },
    ]);
    setInputMessage("");
    setSelectedDateTime(null);
    setFormData((prev) => ({
      ...prev,
      time: "",
    }));
  };

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
    resetAIAgent();
  };

  const handleConfirmBooking = async () => {
    if (
      !formData.name ||
      !formData.phone ||
      !formData.serviceId ||
      !formData.date ||
      !formData.time
    ) {
      alert("Nedostaju podaci za zakazivanje");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/book", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          userId: user?.user?._id || user?._id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška prilikom zakazivanja termina");
      }

      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "✅ **Termin je uspešno zakazan!** 🎉\n\nHvala vam što ste izabrali naš salon. Vidimo se na terminu!",
        },
      ]);

      setShowConfirmation(false);
      setFormData({
        name: "",
        phone: "",
        serviceId: services.length > 0 ? services[0]._id : "",
        date: "",
        time: "",
      });
      setSelectedDateTime(null);

      if (onComplete) {
        onComplete(true);
      }
    } catch (error) {
      console.error("Greška pri zakazivanju:", error);
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: `❌ **Greška pri zakazivanju:** ${error.message}\n\nMolim vas pokušajte ponovo ili kontaktirajte salon direktno.`,
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="relative space-y-6">
      {/* Osnovna forma */}
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4 text-[var(--foreground)]">
          📝 Osnovni podaci
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2 text-[var(--foreground)]">
              Vaše ime *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleFormChange}
              required
              className="w-full p-3 border rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Unesite Vaše ime"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2 text-[var(--foreground)]">
              Telefon *
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleFormChange}
              required
              className="w-full p-3 border rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Npr. 0601234567"
            />
          </div>
        </div>

        {services.length > 0 && (
          <div className="mt-4">
            <label className="block text-sm font-medium mb-2 text-[var(--foreground)]">
              Usluga *
            </label>
            <select
              name="serviceId"
              value={formData.serviceId}
              onChange={handleFormChange}
              required
              className="w-full p-3 border rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">— Izaberite uslugu —</option>
              {services.map((service) => (
                <option key={service._id} value={service._id}>
                  {service.name} - {service.durationMins} min (
                  {service.price ? `${service.price} RSD` : "Cena na upit"})
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="mt-4">
          <label className="block text-sm font-medium mb-2 text-[var(--foreground)]">
            Datum *
          </label>
          <input
            type="date"
            name="date"
            value={formData.date}
            onChange={handleFormChange}
            required
            min={new Date().toISOString().split("T")[0]}
            className="w-full p-3 border rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Prikaz izabranog termina */}
        {selectedDateTime && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
              🗓️ Izabrani termin:
            </h4>
            <p className="text-blue-700 dark:text-blue-300">
              {new Date(selectedDateTime.date).toLocaleDateString("sr-RS")} u{" "}
              {selectedDateTime.time}h
            </p>
          </div>
        )}
      </div>

      {/* AI Chat za termine */}
      <div className="flex flex-col h-[400px] bg-[var(--surface)] rounded-lg shadow-lg border">
        {/* Chat header */}
        <div className="p-4 border-b border-[var(--foreground)]/10 bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-t-lg">
          <h3 className="font-semibold text-[var(--foreground)] flex items-center gap-2">
            🤖 AI Asistent za vreme
            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
              ONLINE
            </span>
          </h3>
          <p className="text-sm text-[var(--foreground)]/70 mt-1">
            Recite mi u koje vreme želite da dođete za izabrani datum
          </p>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`flex ${
                message.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`max-w-[80%] p-4 rounded-2xl ${
                  message.role === "user"
                    ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg ml-4"
                    : "bg-white dark:bg-gray-800 text-[var(--foreground)] border border-gray-200 dark:border-gray-700 shadow-md mr-4"
                }`}
              >
                {message.role === "assistant" && (
                  <div className="flex items-center gap-2 mb-2 text-xs text-gray-500 dark:text-gray-400">
                    <div className="w-6 h-6 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                      AI
                    </div>
                    <span>Asistent</span>
                  </div>
                )}

                <div className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content.split("**").map((part, i) =>
                    i % 2 === 1 ? (
                      <strong key={i} className="font-semibold">
                        {part}
                      </strong>
                    ) : (
                      part
                    )
                  )}
                </div>

                {/* Quick action button za sledeći dostupan termin */}
                {message.role === "assistant" && message.nextAvailable && (
                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <button
                      onClick={() => handleQuickBookNext(message.nextAvailable)}
                      className="text-xs bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white px-4 py-2 rounded-full transition-all transform hover:scale-105 font-medium"
                    >
                      ⚡ Zakaži ovaj termin
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="max-w-[80%] p-4 rounded-2xl bg-white dark:bg-gray-800 text-[var(--foreground)] border border-gray-200 dark:border-gray-700 shadow-md mr-4">
                <div className="flex items-center gap-2 mb-2 text-xs text-gray-500 dark:text-gray-400">
                  <div className="w-6 h-6 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    AI
                  </div>
                  <span>Asistent</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Proveravam dostupnost...
                  </span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 border-t border-[var(--foreground)]/10 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex space-x-3 items-center">
            <div className="flex-1">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  !formData.name ||
                  !formData.phone ||
                  !formData.serviceId ||
                  !formData.date
                    ? "Unesite osnovne podatke"
                    : "U koje vreme želite da dođete?"
                }
                className="w-full px-4 py-2.5 border rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                disabled={
                  isLoading ||
                  !formData.name ||
                  !formData.phone ||
                  !formData.serviceId ||
                  !formData.date
                }
              />
            </div>
            <button
              onClick={handleSendMessage}
              disabled={
                isLoading ||
                !inputMessage.trim() ||
                !formData.name ||
                !formData.phone ||
                !formData.serviceId ||
                !formData.date
              }
              className="px-4 py-2.5 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 active:scale-95 flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Traži</span>
                </>
              ) : (
                <>
                  <span>Traži</span>
                  <span>🔍</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Modal - Overlay */}
      {showConfirmation && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={handleCancelConfirmation}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <h4 className="font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center gap-2 text-lg">
                ✅ Potvrda zakazivanja
              </h4>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    Ime:
                  </span>
                  <span className="text-gray-900 dark:text-gray-100">
                    {formData.name}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    Telefon:
                  </span>
                  <span className="text-gray-900 dark:text-gray-100">
                    {formData.phone}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    Usluga:
                  </span>
                  <span className="text-gray-900 dark:text-gray-100 text-right">
                    {services.find((s) => s._id === formData.serviceId)?.name}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    Datum:
                  </span>
                  <span className="text-gray-900 dark:text-gray-100">
                    {new Date(formData.date).toLocaleDateString("sr-RS")}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    Vreme:
                  </span>
                  <span className="text-gray-900 dark:text-gray-100 font-semibold">
                    {formData.time}h
                  </span>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleConfirmBooking}
                  disabled={isLoading}
                  className="flex-1 py-3 px-4 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 active:scale-95"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Zakazuje se...</span>
                    </div>
                  ) : (
                    "✅ Potvrdi termin"
                  )}
                </button>
                <button
                  onClick={handleCancelConfirmation}
                  disabled={isLoading}
                  className="px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
                >
                  Otkaži
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
