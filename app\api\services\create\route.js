// app/api/services/create/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";
import mongoose from "mongoose";

export async function POST(request) {
  await dbConnect();

  const body = await request.json();
  const { userId, name, description, durationMins, price } = body;

  // Validacija podataka
  if (!userId || !name || !durationMins) {
    return NextResponse.json(
      { error: "Nedostaju obavezni podaci" },
      { status: 400 }
    );
  }

  try {
    // Pronađi korisnika
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Kreiraj novu uslugu
    const newService = {
      _id: new mongoose.Types.ObjectId(),
      name,
      description: description || "",
      durationMins: parseInt(durationMins, 10) || 30,
      price: price || "",
      createdAt: new Date(),
    };

    // Dodaj uslugu u niz usluga korisnika
    if (!user.services) {
      user.services = [];
    }

    user.services.push(newService);

    // Sačuvaj promene
    await user.save();

    return NextResponse.json(
      {
        success: true,
        message: "Usluga je uspešno dodata",
        serviceId: newService._id,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating service:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
