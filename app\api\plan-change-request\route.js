import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import PlanChangeRequest from "../../auth/models/PlanChangeRequest";

export async function POST(request) {
  try {
    await dbConnect();

    const body = await request.json();
    const {
      userId,
      currentPlan,
      requestedPlan,
      userName,
      userEmail,
      businessName,
    } = body;

    // Validacija podataka
    if (!userId || !currentPlan || !requestedPlan || !userName || !userEmail) {
      return NextResponse.json(
        { error: "Sva obavezna polja moraju biti popunjena" },
        { status: 400 }
      );
    }

    // Kreiranje novog zahteva za promenu plana u MongoDB
    const planChangeRequest = new PlanChangeRequest({
      userId,
      name: userName,
      email: userEmail,
      businessName: businessName || "",
      currentPlan,
      requestedPlan,
      status: "novo",
    });

    // Čuvanje u MongoDB
    const savedRequest = await planChangeRequest.save();

    console.log("Plan change request saved to MongoDB:", savedRequest);

    return NextResponse.json({
      success: true,
      message: "Zahtev za promenu plana je uspešno poslat",
      requestId: savedRequest.id,
    });
  } catch (error) {
    console.error("Error creating plan change request:", error);
    return NextResponse.json(
      { error: "Greška prilikom kreiranja zahteva za promenu plana" },
      { status: 500 }
    );
  }
}
