// app/api/services/update/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";

export async function POST(request) {
  await dbConnect();

  const body = await request.json();
  const { userId, serviceId, name, description, durationMins, price } = body;

  // Validacija podataka
  if (!userId || !serviceId || !name || !durationMins) {
    return NextResponse.json(
      { error: "Nedostaju obavezni podaci" },
      { status: 400 }
    );
  }

  try {
    // Pronađi korisnika
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Proveri da li postoji niz usluga
    if (!user.services || !Array.isArray(user.services)) {
      return NextResponse.json(
        { error: "Usluge nisu pronađene" },
        { status: 404 }
      );
    }

    // Pronađi indeks usluge koju treba ažurirati
    const serviceIndex = user.services.findIndex(
      (service) => service._id.toString() === serviceId
    );

    if (serviceIndex === -1) {
      return NextResponse.json(
        { error: "Usluga nije pronađena" },
        { status: 404 }
      );
    }

    // Ažuriraj uslugu
    user.services[serviceIndex].name = name;
    user.services[serviceIndex].description = description || "";
    user.services[serviceIndex].durationMins = parseInt(durationMins, 10) || 30;
    user.services[serviceIndex].price = price || "";
    user.services[serviceIndex].updatedAt = new Date();

    // Sačuvaj promene
    await user.save();

    return NextResponse.json({
      success: true,
      message: "Usluga je uspešno ažurirana",
    });
  } catch (error) {
    console.error("Error updating service:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
