import mongoose from "mongoose";

const pastBookingSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    clientName: {
      type: String,
      required: true,
    },
    clientEmail: String,
    clientPhone: {
      type: String,
      required: true,
    },
    serviceId: mongoose.Schema.Types.ObjectId,
    serviceName: {
      type: String,
      required: true,
    },
    date: {
      type: Date,
      required: true,
    },
    time: String,
    status: {
      type: String,
      enum: ["confirmed", "cancelled"],
      required: true,
    },
    // Dodajemo polje za praćenje kada je termin prebačen u past
    movedToPastAt: {
      type: Date,
      default: Date.now,
    },
    // Originalni booking ID za referencu
    originalBookingId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
  },
  {
    timestamps: false,
  }
);

// Index za brže pretraživanje po userId i datumu
pastBookingSchema.index({ userId: 1, date: -1 });
pastBookingSchema.index({ userId: 1, movedToPastAt: -1 });

export default mongoose.models.PastBooking ||
  mongoose.model("PastBooking", pastBookingSchema);
