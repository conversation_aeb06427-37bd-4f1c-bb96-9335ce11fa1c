import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";

export async function GET(request) {
  try {
    // Povezivanje sa bazom
    await dbConnect();

    // Dohvatanje svih korisnika (osim admin-a)
    const users = await User.find(
      { role: { $ne: "admin" } },
      "name email plan role createdAt"
    ).sort({ name: 1 });

    return NextResponse.json({
      users,
      count: users.length,
      success: true,
    });
  } catch (error) {
    console.error("Admin Users Error:", error);
    return NextResponse.json(
      { error: "Greška pri dohvatanju korisnika" },
      { status: 500 }
    );
  }
}
