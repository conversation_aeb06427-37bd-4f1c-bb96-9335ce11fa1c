<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6"/>
      <stop offset="100%" style="stop-color:#2563eb"/>
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="2"/>

  <!-- Calendar body -->
  <rect x="8" y="10" width="16" height="14" rx="1.5" fill="#ffffff"/>

  <!-- Calendar header -->
  <rect x="8" y="10" width="16" height="3.5" rx="1.5" fill="#0891b2"/>

  <!-- Calendar binding rings -->
  <rect x="11" y="7" width="1" height="4" rx="0.5" fill="#64748b"/>
  <rect x="15.5" y="7" width="1" height="4" rx="0.5" fill="#64748b"/>
  <rect x="20" y="7" width="1" height="4" rx="0.5" fill="#64748b"/>

  <!-- Calendar grid -->
  <g stroke="#e2e8f0" stroke-width="0.5">
    <line x1="10.5" y1="16" x2="21.5" y2="16"/>
    <line x1="10.5" y1="18.5" x2="21.5" y2="18.5"/>
    <line x1="10.5" y1="21" x2="21.5" y2="21"/>

    <line x1="12.5" y1="14" x2="12.5" y2="23"/>
    <line x1="15" y1="14" x2="15" y2="23"/>
    <line x1="17.5" y1="14" x2="17.5" y2="23"/>
    <line x1="20" y1="14" x2="20" y2="23"/>
  </g>

  <!-- Highlighted appointment date -->
  <circle cx="13.75" cy="17.25" r="1" fill="#14b8a6"/>

  <!-- Other dates -->
  <circle cx="16.25" cy="17.25" r="0.3" fill="#64748b"/>
  <circle cx="18.75" cy="17.25" r="0.3" fill="#64748b"/>
  <circle cx="11.25" cy="19.75" r="0.3" fill="#64748b"/>
  <circle cx="16.25" cy="19.75" r="0.3" fill="#64748b"/>
  <circle cx="18.75" cy="19.75" r="0.3" fill="#64748b"/>
</svg>
