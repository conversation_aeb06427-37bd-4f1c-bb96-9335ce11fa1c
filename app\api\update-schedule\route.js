// app/api/update-schedule/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";

export async function PATCH(request) {
  await dbConnect();

  const body = await request.json();
  const { userId, workingHours } = body;

  if (!userId || !workingHours) {
    return NextResponse.json(
      { error: "Nedostaju obavezni podaci" },
      { status: 400 }
    );
  }

  try {
    const user = await User.findByIdAndUpdate(
      userId,
      { workingHours: workingHours },
      { new: true }
    );

    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      workingHours: user.workingHours,
    });
  } catch (error) {
    console.error("Update schedule error:", error);
    return NextResponse.json({ error: "<PERSON><PERSON><PERSON><PERSON> na serveru" }, { status: 500 });
  }
}
