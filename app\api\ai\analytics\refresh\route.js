import { NextResponse } from "next/server";
import OpenAI from "openai";
import dbConnect from "../../../../auth/lib/mongoose";
import User from "../../../../auth/models/User";
import Booking from "../../../../auth/models/Booking";
import PastBooking from "../../../../auth/models/PastBooking";
import AIAnalyticsCache from "../../../../auth/models/AIAnalyticsCache";
import { trackAIUsage, createDataHash } from "../../../../utils/aiTokens";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Funkcija za kreiranje system prompt-a
function createSystemPrompt(
  analysisType,
  user,
  bookingData,
  userPlan,
  dateRange
) {
  switch (analysisType) {
    case "occupancy":
      return `
<PERSON><PERSON><PERSON>raj obrasce zauzetosti salona na osnovu sledećih podataka o zakazivanjima za period od ${
        dateRange === "7days" ? "POSLEDNJA 7 DANA" : "POSLEDNJIH 30 DANA"
      }:

PODACI O ZAKAZIVANJIMA (${dateRange === "7days" ? "7 dana" : "30 dana"}):
${JSON.stringify(bookingData, null, 2)}

RADNO VREME SALONA:
${JSON.stringify(user.workingHours || {}, null, 2)}

NAPOMENA: Salon već koristi ZakaziAI booking sistem sa AI funkcionalnostima.
PERIOD ANALIZE: ${
        dateRange === "7days"
          ? "Kratkoročna analiza (7 dana)"
          : "Dugoročna analiza (30 dana)"
      }

OBAVEZNO VRATI ODGOVOR U SLEDEĆEM JSON FORMATU (koristi STVARNE podatke iz baze za ${
        dateRange === "7days" ? "7 dana" : "30 dana"
      }, ne primere):
{
  "summaryText": "Analiza zauzetosti za ${
    dateRange === "7days" ? "poslednja 7 dana" : "poslednjih 30 dana"
  } pokazuje...",
  "metrics": {
    "occupancyRate": [IZRAČUNAJ na osnovu stvarnih podataka za ${
      dateRange === "7days" ? "7 dana" : "30 dana"
    }],
    "peakHours": "[ANALIZIRAJ stvarne termine i odredi najzauzetije sate]",
    "busiestDay": "[ANALIZIRAJ stvarne podatke i odredi najzauzetiji dan]",
    "averageBookingsPerDay": [IZRAČUNAJ prosek na osnovu stvarnih podataka za ${
      dateRange === "7days" ? "7 dana" : "30 dana"
    }]
  },
  "trendData": [
    ${
      dateRange === "7days"
        ? `{ "period": "Pon", "occupancy": [STVARNI PODATAK za ponedeljke], "bookings": [STVARNI BROJ za ponedeljke] },
    { "period": "Uto", "occupancy": [STVARNI PODATAK za utorke], "bookings": [STVARNI BROJ za utorke] },
    { "period": "Sre", "occupancy": [STVARNI PODATAK za srede], "bookings": [STVARNI BROJ za srede] },
    { "period": "Čet", "occupancy": [STVARNI PODATAK za četvrtke], "bookings": [STVARNI BROJ za četvrtke] },
    { "period": "Pet", "occupancy": [STVARNI PODATAK za petke], "bookings": [STVARNI BROJ za petke] },
    { "period": "Sub", "occupancy": [STVARNI PODATAK za subote], "bookings": [STVARNI BROJ za subote] },
    { "period": "Ned", "occupancy": [STVARNI PODATAK za nedelje], "bookings": [STVARNI BROJ za nedelje] }`
        : `{ "period": "Nedelja 1", "occupancy": [STVARNI PODATAK za prvu nedelju], "bookings": [STVARNI BROJ za prvu nedelju] },
    { "period": "Nedelja 2", "occupancy": [STVARNI PODATAK za drugu nedelju], "bookings": [STVARNI BROJ za drugu nedelju] },
    { "period": "Nedelja 3", "occupancy": [STVARNI PODATAK za treću nedelju], "bookings": [STVARNI BROJ za treću nedelju] },
    { "period": "Nedelja 4", "occupancy": [STVARNI PODATAK za četvrtu nedelju], "bookings": [STVARNI BROJ za četvrtu nedelju] }`
    }
  ],
  "recommendations": [
    { "text": "[KONKRETNA ${
      dateRange === "7days" ? "kratkoročna" : "dugoročna"
    } preporuka na osnovu analize stvarnih podataka]" },
    { "text": "[KONKRETNA ${
      dateRange === "7days" ? "kratkoročna" : "dugoročna"
    } preporuka na osnovu analize stvarnih podataka]" }
  ]
}

VAŽNO: 
- Analiziraj STVARNE podatke iz bookingData za period od ${
        dateRange === "7days" ? "7 dana" : "30 dana"
      }
- ${
        dateRange === "7days"
          ? "Fokusiraj se na kratkoročne trendove i hitne optimizacije"
          : "Fokusiraj se na dugoročne obrasce i strateške preporuke"
      }
- Izračunaj stvarne brojeve, ne koristi primere!

Analiziraj i daj preporuke za optimalno radno vreme, najzauzetije periode i poboljšanje iskorišćenosti kapaciteta.
`;

    case "revenue":
      return `
Analiziraj prihode i finansijske trendove salona:

PODACI O ZAKAZIVANJIMA:
${JSON.stringify(bookingData, null, 2)}

USLUGE I CENE:
${
  user.services
    ?.map((s) => `${s.name}: ${s.price} RSD (${s.durationMins} min)`)
    .join("\n") || "Nema definisanih usluga"
}

NAPOMENA: Salon već koristi ZakaziAI booking sistem sa AI funkcionalnostima.

OBAVEZNO VRATI ODGOVOR U SLEDEĆEM JSON FORMATU (koristi STVARNE podatke iz baze, ne primere):
{
  "summaryText": "Analiza prihoda na osnovu stvarnih podataka pokazuje...",
  "metrics": {
    "totalRevenue": [IZRAČUNAJ ukupan prihod na osnovu cena usluga i broja termina],
    "averageRevenuePerBooking": [IZRAČUNAJ prosečan prihod po terminu],
    "revenueChangePercent": [PROCENI trend na osnovu podataka ili stavi 0 ako nema dovoljno podataka],
    "topService": "[ANALIZIRAJ koja usluga se najčešće zakazuje]"
  },
  "trendData": [
    { "period": "Nedelja 1", "revenue": [IZRAČUNAJ za prvu nedelju], "bookings": [STVARNI BROJ] },
    { "period": "Nedelja 2", "revenue": [IZRAČUNAJ za drugu nedelju], "bookings": [STVARNI BROJ] },
    { "period": "Nedelja 3", "revenue": [IZRAČUNAJ za treću nedelju], "bookings": [STVARNI BROJ] },
    { "period": "Nedelja 4", "revenue": [IZRAČUNAJ za četvrtu nedelju], "bookings": [STVARNI BROJ] }
  ],
  "recommendations": [
    { "text": "[KONKRETNA preporuka na osnovu analize stvarnih cena i najtraženijih usluga]" },
    { "text": "[KONKRETNA preporuka na osnovu analize stvarnih podataka o prihodima]" }
  ]
}

VAŽNO: Koristi stvarne cene usluga i broj termina za izračunavanje prihoda. Ne koristi izmišljene brojeve!

Analiziraj najtraženije usluge, procenjene prihode i daj preporuke za povećanje.
`;

    case "cancellation":
      return `
Analiziraj stopu otkazivanja i razloge:

PODACI O ZAKAZIVANJIMA:
${JSON.stringify(bookingData, null, 2)}

NAPOMENA: Salon već koristi ZakaziAI booking sistem sa AI funkcionalnostima.

OBAVEZNO VRATI ODGOVOR U SLEDEĆEM JSON FORMATU (koristi STVARNE podatke iz baze, ne primere):
{
  "summaryText": "Analiza otkazivanja na osnovu stvarnih podataka pokazuje...",
  "metrics": {
    "cancellationRate": [IZRAČUNAJ stvarnu stopu otkazivanja iz podataka],
    "mostCancelledService": "[ANALIZIRAJ koja usluga se najčešće otkazuje ili 'N/A' ako nema podataka]",
    "worstDay": "[ANALIZIRAJ koji dan ima najviše otkazivanja]",
    "averageCancellationTime": "[PROCENI na osnovu podataka ili 'N/A' ako nema dovoljno informacija]"
  },
  "trendData": [
    ${
      dateRange === "7days"
        ? `{ "period": "Pon", "cancellations": [STVARNI BROJ otkazanih ponedeljkom], "total": [STVARNI UKUPAN BROJ ponedeljkom] },
    { "period": "Uto", "cancellations": [STVARNI BROJ otkazanih utorkom], "total": [STVARNI UKUPAN BROJ utorkom] },
    { "period": "Sre", "cancellations": [STVARNI BROJ otkazanih sredom], "total": [STVARNI UKUPAN BROJ sredom] },
    { "period": "Čet", "cancellations": [STVARNI BROJ otkazanih četvrtkom], "total": [STVARNI UKUPAN BROJ četvrtkom] },
    { "period": "Pet", "cancellations": [STVARNI BROJ otkazanih petkom], "total": [STVARNI UKUPAN BROJ petkom] },
    { "period": "Sub", "cancellations": [STVARNI BROJ otkazanih subotom], "total": [STVARNI UKUPAN BROJ subotom] },
    { "period": "Ned", "cancellations": [STVARNI BROJ otkazanih nedeljom], "total": [STVARNI UKUPAN BROJ nedeljom] }`
        : `{ "period": "Nedelja 1", "cancellations": [STVARNI BROJ otkazanih u prvoj nedelji], "total": [STVARNI UKUPAN BROJ u prvoj nedelji] },
    { "period": "Nedelja 2", "cancellations": [STVARNI BROJ otkazanih u drugoj nedelji], "total": [STVARNI UKUPAN BROJ u drugoj nedelji] },
    { "period": "Nedelja 3", "cancellations": [STVARNI BROJ otkazanih u trećoj nedelji], "total": [STVARNI UKUPAN BROJ u trećoj nedelji] },
    { "period": "Nedelja 4", "cancellations": [STVARNI BROJ otkazanih u četvrtoj nedelji], "total": [STVARNI UKUPAN BROJ u četvrtoj nedelji] }`
    }
  ],
  "recommendations": [
    { "text": "[KONKRETNA preporuka na osnovu obrazaca otkazivanja iz stvarnih podataka]" }
  ]
}

VAŽNO: Analiziraj status polja u bookingData (cancelled, confirmed, completed) za stvarne brojeve!

Analiziraj stopu otkazivanja, obrasce i daj preporuke za smanjenje.
`;

    case "competitive":
    default:
      return `
Napravi KONKURENTSKU ANALIZU za salon na osnovu podataka o zakazivanjima:

PODACI O SALONU:
- Naziv: ${user.name}
- Plan: ${userPlan}
- Usluge: ${
        user.services
          ?.map((s) => `${s.name} - ${s.price}RSD (${s.durationMins}min)`)
          .join(", ") || "Nema definisanih usluga"
      }
- Radno vreme: ${JSON.stringify(user.workingHours || {})}
- Booking sistem: ZakaziAI (već implementiran i aktivan)
- AI funkcionalnosti: Booking asistent, AI analitika

PODACI O ZAKAZIVANJIMA (${
        dateRange === "7days" ? "poslednja 7 dana" : "poslednjih 30 dana"
      }):
${JSON.stringify(bookingData, null, 2)}

VAŽNO: Salon već koristi ZakaziAI booking sistem sa AI funkcionalnostima. NE preporučuj uvođenje booking sistema!

OBAVEZNO VRATI ODGOVOR U SLEDEĆEM JSON FORMATU (koristi STVARNE podatke iz baze, ne primere):
{
  "summaryText": "Konkurentska analiza na osnovu stvarnih podataka pokazuje da salon...",
  "metrics": {
    "marketPosition": "[PROCENI poziciju na osnovu cena usluga - 'Premium', 'Srednji segment', 'Ekonomski']",
    "priceCompetitiveness": [PROCENI konkurentnost cena kao decimalni broj 0-1],
    "serviceQuality": [PROCENI kvalitet na osnovu broja termina i stope otkazivanja kao decimalni broj 0-1],
    "competitiveAdvantage": "[IDENTIFIKUJ glavnu prednost na osnovu podataka - npr. 'Napredna tehnologija', 'Niske cene', 'Visok kvalitet']"
  },
  "trendData": [
    { "period": "Cene", "yourSalon": [PROSEČNA CENA USLUGA], "competition": [PROCENI prosek za industriju] },
    { "period": "Kvalitet", "yourSalon": [OCENI 0-100 na osnovu podataka], "competition": [PROCENI prosek 60-80] },
    { "period": "Tehnologija", "yourSalon": 95, "competition": [PROCENI prosek 40-70] },
    { "period": "Dostupnost", "yourSalon": [OCENI na osnovu radnog vremena 0-100], "competition": [PROCENI prosek 70-85] }
  ],
  "recommendations": [
    { "text": "[KONKRETNA preporuka na osnovu analize stvarnih cena usluga]" },
    { "text": "[KONKRETNA preporuka na osnovu analize stvarnih podataka o poslovanju]" },
    { "text": "[KONKRETNA preporuka na osnovu analize radnog vremena i zauzetosti]" }
  ]
}

VAŽNO: Koristi stvarne cene usluga, radno vreme i podatke o zakazivanjima za analizu. Ne izmišljaj brojeve!

Fokusiraj se na poziciju na tržištu, cene, kvalitet usluga i konkretne preporuke za poboljšanje konkurentnosti.
`;
  }
}

// Funkcija za generisanje AI analize
async function generateAnalysis(user, analysisType, dateRange) {
  const daysBack = dateRange === "7days" ? 7 : 30;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - daysBack);

  // Dohvatanje zakazivanja za analizu iz obe kolekcije (filtriramo po datumu termina)
  const [currentBookings, pastBookings] = await Promise.all([
    Booking.find({
      userId: user._id,
      date: { $gte: startDate },
    }),
    PastBooking.find({
      userId: user._id,
      date: { $gte: startDate },
    }),
  ]);

  // Kombinovanje podataka iz obe kolekcije
  const allBookings = [...currentBookings, ...pastBookings];

  // Priprema podataka za analizu
  const bookingData = allBookings.map((booking) => ({
    date: booking.date,
    time: booking.time,
    service: booking.serviceName || "Nepoznata usluga",
    status: booking.status,
    clientName: booking.clientName,
  }));

  // Kreiranje hash-a podataka za cache proveru
  const dataHash = createDataHash({
    bookingData,
    analysisType,
    dateRange,
    userServices: user.services,
  });

  const userPlan = user.plan || "standard";
  const systemPrompt = createSystemPrompt(
    analysisType,
    user,
    bookingData,
    userPlan,
    dateRange
  );

  const response = await openai.chat.completions.create({
    model: "gpt-4.1-nano",
    messages: [
      { role: "system", content: systemPrompt },
      {
        role: "user",
        content: "Molim te analiziraj podatke i daj preporuke u JSON formatu.",
      },
    ],
    max_tokens: 800,
    temperature: 0.3,
  });

  let analysis;
  try {
    // Pokušaj da parsiraš JSON odgovor
    const rawContent = response.choices[0].message.content;
    analysis = JSON.parse(rawContent);
  } catch (parseError) {
    console.error("JSON parse error:", parseError);
    // Fallback na običan tekst ako JSON parsing ne uspe
    analysis = {
      summaryText: response.choices[0].message.content,
      metrics: {},
      trendData: [],
      recommendations: [],
    };
  }

  // Kreiranje osnovnih statistika (koristimo allBookings umesto bookings)
  const stats = {
    totalBookings: allBookings.length,
    confirmedBookings: allBookings.filter(
      (b) => b.status === "confirmed" || b.status === "completed"
    ).length,
    cancelledBookings: allBookings.filter((b) => b.status === "cancelled")
      .length,
    completedBookings: allBookings.filter(
      (b) => b.status === "confirmed" || b.status === "completed"
    ).length, // Isti kao confirmedBookings za backward compatibility
    cancellationRate:
      allBookings.length > 0
        ? (
            (allBookings.filter((b) => b.status === "cancelled").length /
              allBookings.length) *
            100
          ).toFixed(1)
        : 0,
  };

  // Tracking AI usage
  await trackAIUsage(user._id, "analytics", response.usage, "gpt-4.1-nano", {
    analysisType,
    dateRange,
    bookingCount: allBookings.length,
  });

  return { analysis, stats, dataHash };
}

// API endpoint za automatsko osvežavanje
export async function POST(request) {
  try {
    // Proveri da li je zahtev došao iz cron job-a ili interno
    const { cronSecret } = await request.json();

    if (cronSecret !== process.env.CRON_SECRET) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbConnect();

    // Dohvati sve korisnike koji imaju Standard Plus ili viši plan
    const users = await User.find({
      plan: { $in: ["standardplus", "premium"] },
    });

    let refreshedCount = 0;
    const analysisTypes = [
      "competitive",
      "occupancy",
      "revenue",
      "cancellation",
    ];
    const dateRanges = ["7days", "30days"];

    for (const user of users) {
      for (const analysisType of analysisTypes) {
        for (const dateRange of dateRanges) {
          try {
            // Obriši stari cache
            await AIAnalyticsCache.deleteMany({
              userId: user._id,
              analysisType,
              dateRange,
            });

            // Generiši novu analizu
            const { analysis, stats, dataHash } = await generateAnalysis(
              user,
              analysisType,
              dateRange
            );

            // Sačuvaj novi cache
            const cacheEntry = new AIAnalyticsCache({
              userId: user._id,
              analysisType,
              dateRange,
              analysis,
              stats,
              dataHash,
            });
            await cacheEntry.save();

            refreshedCount++;
          } catch (error) {
            console.error(
              `Error refreshing analytics for user ${user._id}, type ${analysisType}:`,
              error
            );
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: `Osveženo ${refreshedCount} analiza za ${users.length} korisnika`,
      refreshedCount,
      userCount: users.length,
    });
  } catch (error) {
    console.error("Auto refresh error:", error);
    return NextResponse.json(
      { error: "Greška pri automatskom osvežavanju" },
      { status: 500 }
    );
  }
}
