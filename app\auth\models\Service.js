import mongoose from "mongoose";

const ServiceSchema = new mongoose.Schema({
  salon: { type: mongoose.Types.ObjectId, ref: "Salon", required: true },
  name: { type: String, required: true },
  durationMins: { type: Number, required: true }, // trajanje u minutima
  price: { type: Number, required: true }, // opcionalno
});

export default mongoose.models.Service ||
  mongoose.model("Service", ServiceSchema);
