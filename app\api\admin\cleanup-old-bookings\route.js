import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import Booking from "../../../auth/models/Booking";
import PastBooking from "../../../auth/models/PastBooking";

export async function DELETE(request) {
  try {
    await dbConnect();

    // Računanje datuma pre 30 dana
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Brisanje starih termina iz obe kolekcije
    const [deletedBookings, deletedPastBookings] = await Promise.all([
      Booking.deleteMany({
        date: { $lt: thirtyDaysAgo }
      }),
      PastBooking.deleteMany({
        date: { $lt: thirtyDaysAgo }
      })
    ]);

    const totalDeleted = deletedBookings.deletedCount + deletedPastBookings.deletedCount;

    return NextResponse.json({
      success: true,
      message: `Uspešno obrisano ${totalDeleted} termina starijih od 30 dana`,
      details: {
        deletedFromBookings: deletedBookings.deletedCount,
        deletedFromPastBookings: deletedPastBookings.deletedCount,
        totalDeleted,
        cutoffDate: thirtyDaysAgo.toISOString()
      }
    });

  } catch (error) {
    console.error("Error cleaning up old bookings:", error);
    return NextResponse.json(
      { error: "Greška prilikom brisanja starih termina" },
      { status: 500 }
    );
  }
}

// GET endpoint za pregled koliko termina bi bilo obrisano
export async function GET(request) {
  try {
    await dbConnect();

    // Računanje datuma pre 30 dana
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Brojanje starih termina u obe kolekcije
    const [oldBookingsCount, oldPastBookingsCount] = await Promise.all([
      Booking.countDocuments({
        date: { $lt: thirtyDaysAgo }
      }),
      PastBooking.countDocuments({
        date: { $lt: thirtyDaysAgo }
      })
    ]);

    const totalOldBookings = oldBookingsCount + oldPastBookingsCount;

    return NextResponse.json({
      success: true,
      oldBookingsCount,
      oldPastBookingsCount,
      totalOldBookings,
      cutoffDate: thirtyDaysAgo.toISOString(),
      message: totalOldBookings > 0 
        ? `Pronađeno je ${totalOldBookings} termina starijih od 30 dana koji mogu biti obrisani`
        : "Nema termina starijih od 30 dana za brisanje"
    });

  } catch (error) {
    console.error("Error checking old bookings:", error);
    return NextResponse.json(
      { error: "Greška prilikom provere starih termina" },
      { status: 500 }
    );
  }
}
