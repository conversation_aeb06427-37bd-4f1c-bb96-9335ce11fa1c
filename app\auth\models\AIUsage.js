import mongoose from "mongoose";

const AIUsageSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  feature: {
    type: String,
    required: true,
    enum: ["booking-assistant", "analytics"],
  },
  tokensUsed: {
    type: Number,
    required: true,
  },
  model: {
    type: String,
    required: true,
    default: "gpt-4.1-nano",
  },
  cost: {
    type: Number, // u dolarima
    required: true,
  },
  requestData: {
    analysisType: String, // za analytics
    messageLength: Number, // za booking assistant
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Index za brže pretraživanje
AIUsageSchema.index({ userId: 1, createdAt: -1 });
AIUsageSchema.index({ userId: 1, feature: 1, createdAt: -1 });

export default mongoose.models.AIUsage ||
  mongoose.model("AIUsage", AIUsageSchema);
