"use client";

import React, { useState, useEffect } from "react";

export default function UsersManager({ setShowRegisterForm }) {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/users");
      const data = await response.json();

      if (data.success) {
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error("Greška pri učitavanju korisnika:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  if (loading) {
    return (
      <div className="bg-[var(--surface)] rounded-lg shadow p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-500 mx-auto mb-4"></div>
          <p className="text-[var(--foreground)]/70">Učitavanje korisnika...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-[var(--foreground)]">
            Upravljanje korisnicima
          </h2>
          <p className="text-[var(--foreground)]/70 mt-1">
            Pregled i upravljanje registrovanim korisnicima
          </p>
        </div>
        <button
          onClick={() => setShowRegisterForm(true)}
          className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
        >
          Dodaj novog korisnika
        </button>
      </div>

      <div className="bg-[var(--surface)] rounded-lg shadow">
        {/* Stats */}
        <div className="px-6 py-4 border-b border-black/5 dark:border-white/10">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-[var(--foreground)]">
                {users.length}
              </div>
              <div className="text-sm text-[var(--foreground)]/70">Ukupno</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {users.filter((u) => u.plan === "standard").length}
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Standard
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {users.filter((u) => u.plan === "premium").length}
              </div>
              <div className="text-sm text-[var(--foreground)]/70">Premium</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {users.filter((u) => u.plan === "enterprise").length}
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Enterprise
              </div>
            </div>
          </div>
        </div>

        {/* Users List */}
        <div className="p-6">
          {users.length === 0 ? (
            <div className="text-center py-8">
              <svg
                className="w-12 h-12 text-[var(--foreground)]/30 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                />
              </svg>
              <p className="text-[var(--foreground)]/70">
                Nema registrovanih korisnika
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((user) => (
                <div
                  key={user._id}
                  className="border border-black/10 dark:border-white/10 rounded-lg p-4 hover:bg-[var(--muted)] transition-colors"
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium text-[var(--foreground)]">
                          {user.name}
                        </h3>
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.plan === "enterprise"
                              ? "bg-yellow-100 text-yellow-800"
                              : user.plan === "premium"
                              ? "bg-purple-100 text-purple-800"
                              : user.plan === "standard-plus"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {user.plan}
                        </span>
                      </div>
                      <div className="text-sm text-[var(--foreground)]/70 space-y-1">
                        <div>
                          <strong>Email:</strong> {user.email}
                        </div>
                        <div>
                          <strong>Biznis:</strong> {user.businessName || "N/A"}
                        </div>
                        <div>
                          <strong>Registrovan:</strong>{" "}
                          {new Date(user.createdAt).toLocaleDateString("sr-RS")}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <button
                        onClick={() => {
                          // Možemo dodati funkcionalnost za pregled detalja korisnika
                          console.log("Pregled korisnika:", user._id);
                        }}
                        className="px-3 py-1 text-sm bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400 rounded hover:bg-teal-200 dark:hover:bg-teal-900/40 transition-colors"
                      >
                        Detalji
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
