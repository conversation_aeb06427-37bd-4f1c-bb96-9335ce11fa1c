<svg width="800" height="450" viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="800" height="450" rx="8" fill="url(#paint0_linear)"/>
  
  <!-- Header -->
  <rect x="20" y="20" width="760" height="60" rx="4" fill="white" fill-opacity="0.1"/>
  <rect x="40" y="40" width="120" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="600" y="40" width="80" height="20" rx="10" fill="white" fill-opacity="0.3"/>
  <rect x="700" y="40" width="60" height="20" rx="10" fill="white" fill-opacity="0.3"/>
  
  <!-- Sidebar -->
  <rect x="20" y="100" width="200" height="330" rx="4" fill="white" fill-opacity="0.1"/>
  <rect x="40" y="120" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="40" y="160" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="40" y="200" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="40" y="240" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="40" y="280" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="40" y="320" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="40" y="360" width="160" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  
  <!-- Main Content -->
  <rect x="240" y="100" width="540" height="80" rx="4" fill="white" fill-opacity="0.1"/>
  <rect x="260" y="120" width="200" height="20" rx="2" fill="white" fill-opacity="0.3"/>
  <rect x="260" y="150" width="300" height="10" rx="2" fill="white" fill-opacity="0.2"/>
  
  <!-- Calendar -->
  <rect x="240" y="200" width="540" height="230" rx="4" fill="white" fill-opacity="0.1"/>
  
  <!-- Calendar Header -->
  <rect x="260" y="220" width="500" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  
  <!-- Calendar Grid -->
  <rect x="260" y="260" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="341" y="260" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="422" y="260" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="503" y="260" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="584" y="260" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="665" y="260" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  
  <rect x="260" y="300" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="341" y="300" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="422" y="300" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="503" y="300" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="584" y="300" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="665" y="300" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  
  <rect x="260" y="340" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="341" y="340" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="422" y="340" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="503" y="340" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="584" y="340" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="665" y="340" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  
  <rect x="260" y="380" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="341" y="380" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="422" y="380" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="503" y="380" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="584" y="380" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  <rect x="665" y="380" width="71" height="30" rx="2" fill="white" fill-opacity="0.2"/>
  
  <!-- Appointments -->
  <rect x="341" y="300" width="142" height="25" rx="2" fill="#0ea5e9" fill-opacity="0.7"/>
  <rect x="503" y="340" width="71" height="25" rx="2" fill="#0d9488" fill-opacity="0.7"/>
  <rect x="584" y="380" width="142" height="25" rx="2" fill="#0ea5e9" fill-opacity="0.7"/>
  
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="800" y2="450" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0d9488"/>
      <stop offset="1" stop-color="#0ea5e9"/>
    </linearGradient>
  </defs>
</svg>