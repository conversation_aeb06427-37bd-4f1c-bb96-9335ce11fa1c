import { NextResponse } from "next/server";

export async function GET(request) {
  const ip = request.ip || 
             request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';

  return NextResponse.json({
    success: true,
    message: "Rate limiting test endpoint - ovaj endpoint NIJE ograničen",
    ip: ip,
    timestamp: new Date().toISOString(),
    endpoint: "/api/test-rate-limit"
  });
}

export async function POST(request) {
  const ip = request.ip || 
             request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';

  const body = await request.json();

  return NextResponse.json({
    success: true,
    message: "POST test endpoint - ovaj endpoint NIJE ograničen",
    ip: ip,
    timestamp: new Date().toISOString(),
    endpoint: "/api/test-rate-limit",
    receivedData: body
  });
}
