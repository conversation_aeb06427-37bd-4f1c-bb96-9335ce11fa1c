"use client";

import React, { useState } from "react";

export default function Settings({ user }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ text: "", type: "" });

  const currentPlan = user?.user?.plan || user?.plan || "standard";

  const plans = [
    {
      id: "standard",
      name: "Standard",
      price: "2.400 RSD",
      features: [
        "AI provera dostupnosti",
        "Do 240 mesečno",
        "Automatsko slanje linka",
        "Jednostavna instalacija",
      ],
      color: "teal",
      isPopular: false,
    },
    {
      id: "standard-plus",
      name: "Standard Plus",
      price: "3.000 RSD",
      features: [
        "Sve iz Standard plana",
        "Do 450 termina mesečno",
        "AI analitika",
        "Napredne statistike",
      ],
      color: "blue",
      isPopular: true,
    },
    {
      id: "premium",
      name: "Premium",
      price: "4.500 RSD",
      features: [
        "Neograničeno termina",
        "Premium podrška",
        "AI komunikacija sa klijentima",
        "Pamćenje istorije klijenta",
      ],
      color: "purple",
      isPopular: false,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      price: "Po proceni",
      features: [
        "Sve iz Premium paketa",
        "Za salone sa više radnika i lokacija",
        "Napredna izveštavanja i analiza",
        "Integracija s drugim sistemima",
        "Prilagođene usluge",
      ],
      color: "yellow",
      isPopular: false,
    },
  ];

  const handlePlanRequest = async (planId) => {
    if (planId === currentPlan) {
      setMessage({
        text: "Već imate ovaj plan",
        type: "info",
      });
      return;
    }

    setIsSubmitting(true);
    setMessage({ text: "", type: "" });

    try {
      const response = await fetch("/api/plan-change-request", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user?.user?._id || user?._id,
          currentPlan,
          requestedPlan: planId,
          userName: user?.user?.name || user?.name,
          userEmail: user?.user?.email || user?.email,
          businessName: user?.user?.businessName || user?.businessName,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({
          text: "Zahtev za promenu plana je uspešno poslat. Kontaktiraćemo vas uskoro.",
          type: "success",
        });
      } else {
        setMessage({
          text: data.error || "Došlo je do greške prilikom slanja zahteva",
          type: "error",
        });
      }
    } catch (error) {
      setMessage({
        text: "Došlo je do greške prilikom komunikacije sa serverom",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Podešavanja Planova
      </h2>

      {/* Message */}
      {message.text && (
        <div
          className={`mb-4 p-4 rounded-lg ${
            message.type === "success"
              ? "bg-green-100 text-green-800 border border-green-200"
              : message.type === "error"
              ? "bg-red-100 text-red-800 border border-red-200"
              : "bg-blue-100 text-blue-800 border border-blue-200"
          }`}
        >
          {message.text}
        </div>
      )}

      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <div className="space-y-8">
          {/* Current Plan Info */}
          <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">
              Vaš trenutni plan: {plans.find((p) => p.id === currentPlan)?.name}
            </h3>
            <p className="text-sm text-green-600">
              Možete zatražiti promenu plana klikom na dugme "Zatraži promenu"
              kod željenog plana.
            </p>
          </div>

          {/* Planovi sekcija */}
          <div>
            <h3 className="text-lg font-medium mb-6">Dostupni planovi</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {plans.map((plan) => {
                const isCurrentPlan = plan.id === currentPlan;
                const getColorClasses = (color) => {
                  switch (color) {
                    case "teal":
                      return {
                        border: "border-[var(--foreground)]/10",
                        price: "text-teal-600",
                        button: isCurrentPlan
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-teal-600 hover:bg-teal-700",
                        background: "bg-[var(--background)]",
                      };
                    case "blue":
                      return {
                        border: plan.isPopular
                          ? "border-2 border-blue-500"
                          : "border-[var(--foreground)]/10",
                        price: "text-blue-600",
                        button: isCurrentPlan
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-blue-600 hover:bg-blue-700",
                        background: "bg-[var(--background)]",
                      };
                    case "purple":
                      return {
                        border: "border-purple-200",
                        price: "text-purple-600",
                        button: isCurrentPlan
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",
                        background:
                          "bg-gradient-to-br from-purple-50 to-pink-50",
                      };
                    case "yellow":
                      return {
                        border: "border-2 border-yellow-400",
                        price: "text-yellow-400",
                        button: isCurrentPlan
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600",
                        background: "bg-gradient-to-br from-gray-900 to-black",
                      };
                    default:
                      return {
                        border: "border-[var(--foreground)]/10",
                        price: "text-[var(--foreground)]",
                        button: "bg-gray-600 hover:bg-gray-700",
                        background: "bg-[var(--background)]",
                      };
                  }
                };

                const colorClasses = getColorClasses(plan.color);
                const textColor =
                  plan.color === "yellow"
                    ? "text-white"
                    : plan.color === "purple"
                    ? "text-purple-800"
                    : "text-[var(--foreground)]";

                return (
                  <div
                    key={plan.id}
                    className={`${colorClasses.background} ${
                      colorClasses.border
                    } rounded-lg p-6 hover:shadow-lg transition-shadow relative ${
                      isCurrentPlan ? "ring-2 ring-green-500" : ""
                    }`}
                  >
                    {/* Popular badge */}
                    {plan.isPopular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Najpopularniji
                        </span>
                      </div>
                    )}

                    {/* Enterprise badge */}
                    {plan.id === "enterprise" && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold">
                          Enterprise
                        </span>
                      </div>
                    )}

                    {/* Current plan badge */}
                    {isCurrentPlan && (
                      <div className="absolute -top-3 right-4">
                        <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Trenutni plan
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-4">
                      <h4 className={`text-xl font-semibold ${textColor} mb-2`}>
                        {plan.name}
                      </h4>
                      <div
                        className={`text-3xl font-bold ${colorClasses.price} mb-1`}
                      >
                        {plan.price}
                      </div>
                      <div
                        className={`text-sm ${
                          plan.color === "yellow"
                            ? "text-gray-300"
                            : plan.color === "purple"
                            ? "text-purple-600/70"
                            : "text-[var(--foreground)]/60"
                        }`}
                      >
                        {plan.id === "enterprise" ? "prilagođeno" : "mesečno"}
                      </div>
                    </div>

                    <ul className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <span
                            className={`mr-2 ${
                              plan.color === "yellow"
                                ? "text-yellow-400"
                                : "text-green-500"
                            }`}
                          >
                            ✓
                          </span>
                          <span className={`text-sm ${textColor}`}>
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>

                    <button
                      onClick={() => handlePlanRequest(plan.id)}
                      disabled={isCurrentPlan || isSubmitting}
                      className={`w-full px-4 py-2 ${
                        colorClasses.button
                      } text-white rounded-md transition-colors ${
                        plan.id === "enterprise" && !isCurrentPlan
                          ? "text-black font-semibold"
                          : ""
                      } ${
                        isCurrentPlan || isSubmitting
                          ? "cursor-not-allowed"
                          : ""
                      }`}
                    >
                      {isCurrentPlan
                        ? "Trenutni plan"
                        : isSubmitting
                        ? "Šalje se..."
                        : plan.id === "enterprise"
                        ? "Kontaktirajte nas"
                        : "Zatraži promenu"}
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
