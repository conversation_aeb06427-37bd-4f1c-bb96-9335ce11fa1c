import Image from "next/image";
import PolitikaPrivatnosti from "./PolitikaPrivatnosti";
import UsloviKoriscenja from "./UsloviKoriscenja";

function Footer() {
  return (
    <footer
      id="footer"
      className="bg-[var(--background)] border-t border-black/5 dark:border-white/5 relative overflow-hidden"
    >
      {/* Subtle background animation */}
      <div className="absolute inset-0">
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-teal-200/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob"></div>
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-blue-200/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob animation-delay-2000"></div>
      </div>

      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex justify-between">
          <div className="animate-fade-in-up">
            <h3 className="text-sm font-semibold text-[var(--foreground)] tracking-wider uppercase mb-4">
              Kontakt
            </h3>
            <ul className="mt-4 space-y-4">
              <li className="animate-fade-in-up animation-delay-200">
                <a
                  href="tel:+381695338913"
                  className="group text-base text-[var(--foreground)]/70 hover:text-teal-600 dark:hover:text-teal-400 transition-all duration-300 flex items-center hover:translate-x-2"
                >
                  <svg
                    className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  +381695338913
                </a>
              </li>
              <li className="animate-fade-in-up animation-delay-400">
                <a
                  href="mailto:<EMAIL>"
                  className="group text-base text-[var(--foreground)]/70 hover:text-teal-600 dark:hover:text-teal-400 transition-all duration-300 flex items-center hover:translate-x-2"
                >
                  <svg
                    className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <EMAIL>
                </a>
              </li>
              <li className="animate-fade-in-up animation-delay-600">
                <a
                  href="#"
                  className="group text-base text-[var(--foreground)]/70 hover:text-teal-600 dark:hover:text-teal-400 transition-all duration-300 flex items-center hover:translate-x-2"
                >
                  <svg
                    className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"
                    />
                  </svg>
                  Instagram
                </a>
              </li>
            </ul>
          </div>
          <div className="animate-fade-in-up animation-delay-200">
            <h3 className="text-sm font-semibold text-[var(--foreground)] tracking-wider uppercase mb-4">
              Pravno
            </h3>
            <ul className="mt-4 space-y-4">
              <li className="animate-fade-in-up animation-delay-400">
                <PolitikaPrivatnosti />
              </li>
              <li className="animate-fade-in-up animation-delay-600">
                <UsloviKoriscenja />
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12 border-t border-black/5 dark:border-white/5 pt-8 flex flex-col md:flex-row justify-between items-center animate-fade-in-up animation-delay-800">
          <div className="flex items-center">
            <h4 className="text-xl font-bold bg-gradient-to-r from-teal-500 to-blue-600 bg-clip-text text-transparent hover:scale-105 transition-transform duration-300">
              Zakaži AI
            </h4>
            {/*   <Image
              src="/zakaziai_logo.png"
              alt="Zakaži AI Logo"
              width={120}
              height={40}
              className="dark:invert-[0.25]"
            /> */}
          </div>
          <p className="mt-4 md:mt-0 text-base text-[var(--foreground)]/70 hover:text-[var(--foreground)] transition-colors duration-300">
            &copy; {new Date().getFullYear()} Zakaži AI. Sva prava zadržana.
          </p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
