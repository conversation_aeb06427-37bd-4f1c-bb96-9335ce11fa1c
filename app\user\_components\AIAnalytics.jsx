"use client";

import { useState, useEffect } from "react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";

export default function AIAnalytics({ user }) {
  const [analysis, setAnalysis] = useState(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("competitive");
  const [dateRange, setDateRange] = useState("30days");
  const [error, setError] = useState("");
  const [cached, setCached] = useState(false);

  // Cache za različite kombinacije tab-a i date range-a
  const [analysisCache, setAnalysisCache] = useState({});

  const analysisTypes = [
    { id: "competitive", name: "Konkurentska analiza", icon: "🏆" },
    { id: "occupancy", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", icon: "📅" },
    { id: "revenue", name: "<PERSON><PERSON><PERSON><PERSON>", icon: "💰" },
    { id: "cancellation", name: "<PERSON>tkazivanja", icon: "❌" },
  ];

  // Funkcija za proveru da li je potrebno ažuriranje
  const shouldRefreshData = (timestamp) => {
    if (!timestamp) return true;

    const now = new Date();
    const lastUpdate = new Date(timestamp);

    // Pronađi poslednji ponedeljak u 9:00
    const lastMonday = new Date(now);
    lastMonday.setDate(now.getDate() - ((now.getDay() + 6) % 7)); // Idi na poslednji ponedeljak
    lastMonday.setHours(9, 0, 0, 0);

    // Ako je danas ponedeljak i još nije 9:00, koristi prošli ponedeljak
    if (now.getDay() === 1 && now.getHours() < 9) {
      lastMonday.setDate(lastMonday.getDate() - 7);
    }

    return lastUpdate < lastMonday;
  };

  const loadAnalysis = async (
    analysisType = "competitive",
    selectedDateRange = dateRange,
    forceRefresh = false
  ) => {
    const cacheKey = `${analysisType}-${selectedDateRange}`;

    // Proveri cache i da li je potrebno ažuriranje
    if (analysisCache[cacheKey] && !forceRefresh) {
      const cachedData = analysisCache[cacheKey];

      // Ako podaci nisu stari (pre poslednjeg ponedeljka u 9:00), koristi ih
      if (!shouldRefreshData(cachedData.timestamp)) {
        setAnalysis(cachedData.analysis);
        setStats(cachedData.stats);
        setCached(true);
        return;
      }
    }

    setLoading(true);
    setError("");
    setCached(false);

    try {
      const userId = user?.user?._id || user?._id;

      const response = await fetch("/api/ai/analytics", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          analysisType,
          dateRange: selectedDateRange,
          forceRefresh:
            forceRefresh ||
            shouldRefreshData(analysisCache[cacheKey]?.timestamp),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška pri učitavanju AI analize");
      }

      setAnalysis(data.analysis);
      setStats(data.stats);
      setCached(data.cached || false);

      // Sačuvaj u lokalni cache sa trenutnim timestamp-om
      setAnalysisCache((prev) => ({
        ...prev,
        [cacheKey]: {
          analysis: data.analysis,
          stats: data.stats,
          timestamp: Date.now(),
        },
      }));
    } catch (error) {
      console.error("Greška pri AI analizi:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalysis(activeTab, dateRange);
  }, [activeTab, dateRange, user]);

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleDateRangeChange = (newDateRange) => {
    setDateRange(newDateRange);
  };

  // Provera plana
  const userPlan = user?.user?.plan || user?.plan || "standard";
  if (userPlan === "standard") {
    return (
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow text-center">
        <div className="text-6xl mb-4">🤖</div>
        <h3 className="text-xl font-semibold mb-2 text-[var(--foreground)]">
          AI Analitika
        </h3>
        <p className="text-[var(--foreground)]/70 mb-4">
          AI analitika je dostupna samo za Standard Plus plan i više.
        </p>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800 text-sm">
            Nadogradite na Standard Plus plan da biste dobili:
          </p>
          <ul className="text-yellow-700 text-sm mt-2 space-y-1">
            <li>• Automatske preporuke za optimalno radno vreme</li>
            <li>• Analizu obrazaca zauzetosti</li>
            <li>• Naprednu statistiku i trendove</li>
            <li>• Preporuke za povećanje prihoda</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <div className="flex items-center mb-4">
          <div className="text-3xl mr-3">🤖</div>
          <div>
            <h3 className="text-xl font-semibold text-[var(--foreground)]">
              AI Analitika
            </h3>
            <p className="text-[var(--foreground)]/70">
              Automatske preporuke i napredna statistika
            </p>
          </div>
        </div>

        {/* Date Range Selector */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex flex-wrap gap-2">
            {analysisTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => handleTabChange(type.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === type.id
                    ? "bg-blue-500 text-white"
                    : "bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--surface-hover)]"
                }`}
              >
                {type.icon} {type.name}
              </button>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-[var(--foreground)]/70">Period:</span>
            <button
              onClick={() => handleDateRangeChange("7days")}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                dateRange === "7days"
                  ? "bg-green-500 text-white"
                  : "bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--surface-hover)]"
              }`}
            >
              7 dana
            </button>
            <button
              onClick={() => handleDateRangeChange("30days")}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                dateRange === "30days"
                  ? "bg-green-500 text-white"
                  : "bg-[var(--background)] text-[var(--foreground)] hover:bg-[var(--surface-hover)]"
              }`}
            >
              30 dana
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-[var(--background)] p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {stats.totalBookings}
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Ukupno termina
              </div>
            </div>
            <div className="bg-[var(--background)] p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {stats.confirmedBookings}
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Potvrđeno
              </div>
            </div>
            <div className="bg-[var(--background)] p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {stats.cancellationRate}%
              </div>
              <div className="text-sm text-[var(--foreground)]/70">
                Stopa otkazivanja
              </div>
            </div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-[var(--foreground)]/70">
              AI analizira vaše podatke...
            </span>
          </div>
        )}

        {/* Analysis Results */}
        {analysis && !loading && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-[var(--background)] p-6 rounded-lg">
              <h4 className="font-semibold text-[var(--foreground)] mb-4 flex items-center">
                <span className="text-2xl mr-2">
                  {analysisTypes.find((t) => t.id === activeTab)?.icon || "🏆"}
                </span>
                {analysisTypes.find((t) => t.id === activeTab)?.name ||
                  "Analiza"}
              </h4>
              <div className="text-[var(--foreground)] leading-relaxed mb-6">
                {typeof analysis === "string" ? analysis : analysis.summaryText}
              </div>

              {/* Metrics Cards */}
              {analysis.metrics && Object.keys(analysis.metrics).length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  {Object.entries(analysis.metrics).map(([key, value]) => (
                    <div
                      key={key}
                      className="bg-[var(--surface)] p-4 rounded-lg"
                    >
                      <div className="text-lg font-bold text-blue-600">
                        {typeof value === "number" && value < 1 && value > 0
                          ? `${(value * 100).toFixed(0)}%`
                          : typeof value === "number"
                          ? value.toLocaleString()
                          : value}
                      </div>
                      <div className="text-sm text-[var(--foreground)]/70 capitalize">
                        {key.replace(/([A-Z])/g, " $1").trim()}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Chart */}
              {analysis.trendData && analysis.trendData.length > 0 && (
                <div className="mb-6">
                  <h5 className="font-medium text-[var(--foreground)] mb-3">
                    Trendovi i podaci
                  </h5>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      {activeTab === "occupancy" ? (
                        <BarChart data={analysis.trendData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar
                            dataKey="occupancy"
                            fill="#3B82F6"
                            name="Zauzetost"
                          />
                          <Bar
                            dataKey="bookings"
                            fill="#10B981"
                            name="Broj termina"
                          />
                        </BarChart>
                      ) : activeTab === "revenue" ? (
                        <LineChart data={analysis.trendData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="revenue"
                            stroke="#3B82F6"
                            name="Prihod (RSD)"
                          />
                          <Line
                            type="monotone"
                            dataKey="bookings"
                            stroke="#10B981"
                            name="Broj termina"
                          />
                        </LineChart>
                      ) : activeTab === "cancellation" ? (
                        <BarChart data={analysis.trendData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar
                            dataKey="cancellations"
                            fill="#EF4444"
                            name="Otkazivanja"
                          />
                          <Bar
                            dataKey="total"
                            fill="#6B7280"
                            name="Ukupno termina"
                          />
                        </BarChart>
                      ) : (
                        <BarChart data={analysis.trendData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar
                            dataKey="yourSalon"
                            fill="#3B82F6"
                            name="Vaš salon"
                          />
                          <Bar
                            dataKey="competition"
                            fill="#EF4444"
                            name="Konkurencija"
                          />
                        </BarChart>
                      )}
                    </ResponsiveContainer>
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {analysis.recommendations &&
                analysis.recommendations.length > 0 && (
                  <div>
                    <h5 className="font-medium text-[var(--foreground)] mb-3">
                      Preporuke
                    </h5>
                    <div className="space-y-2">
                      {analysis.recommendations.map((rec, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <span className="text-green-500 mt-1">•</span>
                          <span className="text-[var(--foreground)]/80 text-sm">
                            {rec.text}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </div>
        )}

        {cached && (
          <p className="text-xs text-[var(--foreground)]/60">
            📋 Podaci su restaruju svakog pondeljka u 9:00.
          </p>
        )}
      </div>

      {/* Tips */}
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <h4 className="font-semibold text-[var(--foreground)] mb-3 flex items-center">
          <span className="text-2xl mr-2">💡</span>
          Saveti za korišćenje AI analitike
        </h4>
        <div className="space-y-2 text-sm text-[var(--foreground)]/70">
          <p>
            • <strong>Konkurentska analiza:</strong> Poredite se sa
            konkurencijom i dobijte preporuke za poboljšanje
          </p>
          <p>
            • <strong>Zauzetost:</strong> Optimizujte radno vreme na osnovu
            obrazaca
          </p>
          <p>
            • <strong>Prihodi:</strong> Analizirajte najtraženije usluge i
            potencijal
          </p>
          <p>
            • <strong>Otkazivanja:</strong> Smanjite stopu otkazivanja uz AI
            preporuke
          </p>
        </div>
      </div>
    </div>
  );
}
