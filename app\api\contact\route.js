import { NextResponse } from "next/server";

export async function POST(request) {
  const ip = request.ip || 
             request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';

  const body = await request.json();
  const { name, email, message } = body;

  // Validacija
  if (!name || !email || !message) {
    return NextResponse.json(
      { error: "Sva polja su obavezna" },
      { status: 400 }
    );
  }

  // Simulacija slanja email-a
  console.log('Contact form submission:', {
    name,
    email,
    message,
    ip,
    timestamp: new Date().toISOString()
  });

  // Ovde bi trebalo dodati logiku za slanje email-a
  // await sendContactEmail({ name, email, message });

  return NextResponse.json({
    success: true,
    message: "<PERSON>a<PERSON> poruka je uspešno poslata. Odgovoriće vam u najkraćem roku.",
    timestamp: new Date().toISOString()
  });
}
