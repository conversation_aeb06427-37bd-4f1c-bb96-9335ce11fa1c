import mongoose from "mongoose";

const RequestSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  phone: {
    type: String,
    required: true,
  },
  businessName: {
    type: String,
    required: true,
  },
  businessType: {
    type: String,
    required: true,
  },
  message: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    enum: ["demo", "free-trial"],
    required: true,
  },
  preferredTime: {
    type: String,
    default: "",
  },
  status: {
    type: String,
    enum: ["novo", "kontaktirano", "završeno"],
    default: "novo",
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
});

// Dodaje virtuelno polje za ID kao string (kompatibilnost sa postojećim kodom)
RequestSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

RequestSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id.toString();
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

export default mongoose.models.Request || mongoose.model("Request", RequestSchema);
