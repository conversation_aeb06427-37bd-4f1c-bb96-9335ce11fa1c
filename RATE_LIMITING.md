# Rate Limiting Dokumentacija - Zakaži AI

## 📋 Pregled

Rate limiting sistem je implementiran da spreči zloupotrebe API-ja i osigura stabilnost aplikacije. Sistem koristi selektivni pristup - ograničava SAMO kritične endpoint-e koji su podložni zloupotrebi.

## 🎯 Ograničeni Endpoint-i

### Kritični Endpoint-i (Strogi Limiti)

| Endpoint | Limit | Vreme | Razlog |
|----------|-------|-------|--------|
| `/api/login` | 5 zahteva | 15 minuta | Sprečavanje brute force napada |
| `/api/register` | 3 zahteva | 1 sat | Sprečavanje spam registracija |
| `/api/contact` | 5 zahteva | 1 sat | Sprečavanje spam poruka |
| `/api/send-email` | 10 zahteva | 1 sat | Sprečavanje email spam-a |
| `/api/book` | 10 zahteva | 1 minut | Sprečavanje spam booking-a |
| `/api/schedule` | 15 zahteva | 1 minut | Umereno ograničavanje zakazivanja |

### Dashboard Endpoint-i (Veći Limiti)

| Endpoint | Limit | Vreme | Razlog |
|----------|-------|-------|--------|
| `/api/bookings/get-all` | 100 zahteva | 1 minut | Često korišćen u dashboard-u |
| `/api/services/get-all` | 100 zahteva | 1 minut | Često korišćen u dashboard-u |
| `/api/me` | 200 zahteva | 1 minut | Vrlo često korišćen |
| `/api/users/profile` | 50 zahteva | 1 minut | Umeren dashboard endpoint |

### AI Endpoint-i (Umeren Limit)

| Endpoint | Limit | Vreme | Razlog |
|----------|-------|-------|--------|
| `/api/ai` | 30 zahteva | 1 minut | Balans između funkcionalnosti i troškova |

## 🚫 NISU Ograničeni

Svi ostali endpoint-i NISU ograničeni, uključujući:
- `/api/test-rate-limit`
- `/api/working-hours/get`
- `/api/saloni`
- Svi ostali endpoint-i koji nisu eksplicitno navedeni

## 🔧 Konfiguracija

### Development Mode
- Rate limiting je **ONEMOGUĆEN** za localhost IP adrese
- Omogućava nesmetano testiranje tokom razvoja

### Production Mode
- Rate limiting je **OMOGUĆEN** za sve IP adrese
- Koristi in-memory store (može se proširiti sa Redis-om)

## 📊 Rate Limit Headers

Kada je zahtev ograničen, API vraća sledeće headers:

```
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 2024-01-15T10:30:00.000Z
Retry-After: 900
```

## 🧪 Testiranje

### Pokretanje Test Skripte

```bash
node test-rate-limit.js
```

### Manuelno Testiranje

```bash
# Test neograničenog endpoint-a (treba da prođe)
curl http://localhost:3000/api/test-rate-limit

# Test ograničenog endpoint-a (treba da se blokira nakon 5 zahteva)
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","message":"Test"}'
```

## ⚙️ Implementacija

### Middleware Lokacija
- `middleware.js` - Glavni Next.js middleware
- `app/middleware/redisRateLimit.js` - Redis implementacija (opciono)
- `app/middleware/rateLimiter.js` - Express rate limiter (backup)

### Ključne Funkcije

```javascript
// Provera da li je endpoint ograničen
let config = null;
for (const [pattern, limits] of Object.entries(rateLimitConfigs)) {
  if (endpoint.startsWith(pattern)) {
    config = limits;
    break;
  }
}

// Ako endpoint NIJE u listi, PRESKAČE rate limiting
if (!config) {
  return NextResponse.next();
}
```

## 🔄 Dodavanje Novog Ograničenja

Za dodavanje novog endpoint-a u rate limiting:

1. Otvori `middleware.js`
2. Dodaj endpoint u `rateLimitConfigs`:

```javascript
const rateLimitConfigs = {
  // Postojeći endpoint-i...
  '/api/novi-endpoint': { max: 10, windowMs: 60 * 1000 }, // 10 zahteva u 1 minut
};
```

## 🚨 Error Responses

### Rate Limit Exceeded (429)

```json
{
  "error": "Previše zahteva",
  "message": "Dostigli ste maksimalan broj zahteva. Pokušajte ponovo kasnije.",
  "retryAfter": 900
}
```

## 📈 Monitoring

### Admin Endpoint za Monitoring

```bash
# Dobijanje statusa rate limit-a
GET /api/admin/rate-limits?ip=***********&endpoint=/api/login

# Brisanje rate limit-a (admin funkcija)
DELETE /api/admin/rate-limits?ip=***********&endpoint=/api/login
```

## 🔒 Bezbednost

### IP Adresa Detection
Sistem koristi sledeći redosled za dobijanje IP adrese:
1. `request.ip`
2. `x-forwarded-for` header (prvi IP)
3. `x-real-ip` header
4. 'unknown' kao fallback

### Bypass za Development
```javascript
if (process.env.NODE_ENV === 'development' && 
    (ip === '127.0.0.1' || ip === '::1' || ip === 'localhost')) {
  return NextResponse.next();
}
```

## 🚀 Production Deployment

### Environment Variables

```bash
# .env.production
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
RATE_LIMIT_ENABLED=true
```

### Redis Setup (Opciono)
Za production, preporučuje se korišćenje Redis-a umesto in-memory store-a:

```javascript
import { advancedRateLimit } from './app/middleware/redisRateLimit';

const result = await advancedRateLimit(ip, endpoint, config);
```

## 📞 Support

Za pitanja o rate limiting sistemu:
- Email: <EMAIL>
- Dokumentacija: Ovaj fajl
- Test skript: `test-rate-limit.js`
