"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import ThemeToggle from "./ThemeToggle";
import LoginForm from "../login/LoginForm";

function Nav() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showLoginForm, setShowLoginForm] = useState(false);

  const handleLoginClick = (e) => {
    e.preventDefault();
    setShowLoginForm(true);
  };

  const scrollTotop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <>
      <nav className="sticky top-0 z-50 bg-[var(--background)]/80 backdrop-blur-md border-b border-black/5 dark:border-white/5 animate-fade-in-up">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <h4
                  onClick={scrollTotop}
                  className="cursor-pointer text-lg sm:text-xl font-bold bg-gradient-to-r from-teal-500 to-blue-600 bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 min-w-0"
                >
                  <span className="whitespace-nowrap">Zakaži AI</span>
                </h4>
                {/*  <Image
                  src="/zakaziai_logo.png"
                  alt="Zakaži AI Logo"
                  width={120}
                  height={40}
                  className="dark:invert-[0.25]"
                /> */}
              </div>
              <div className="hidden sm:ml-10 sm:flex sm:space-x-8">
                <a
                  href="#za-koga"
                  className="group border-transparent hover:border-teal-500 text-[var(--foreground)]/80 hover:text-[var(--foreground)] inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-300 hover:-translate-y-0.5"
                >
                  <span className="relative">
                    Kako funkcioniše?
                    <span className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-teal-500 to-blue-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                  </span>
                </a>
                <a
                  href="#cene"
                  className="group border-transparent hover:border-teal-500 text-[var(--foreground)]/80 hover:text-[var(--foreground)] inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-300 hover:-translate-y-0.5"
                >
                  <span className="relative">
                    Cene
                    <span className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-teal-500 to-blue-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                  </span>
                </a>
                <a
                  href="#footer"
                  className="group border-transparent hover:border-teal-500 text-[var(--foreground)]/80 hover:text-[var(--foreground)] inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-300 hover:-translate-y-0.5"
                >
                  <span className="relative">
                    Kontakt
                    <span className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-teal-500 to-blue-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                  </span>
                </a>
              </div>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4">
              <ThemeToggle />

              <button
                onClick={handleLoginClick}
                className="group hover:text-white px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 cursor-pointer bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-xl"
              >
                <span className="relative z-10">Prijava</span>
              </button>
            </div>
            <div className="flex items-center sm:hidden">
              <ThemeToggle />
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="ml-2 inline-flex items-center justify-center p-2 rounded-md text-[var(--foreground)]/80 hover:text-[var(--foreground)] hover:bg-[var(--surface-hover)] focus:outline-none transition-colors"
              >
                <span className="sr-only">Otvori glavni meni</span>
                <svg
                  className={`${isMenuOpen ? "hidden" : "block"} h-6 w-6`}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
                <svg
                  className={`${isMenuOpen ? "block" : "hidden"} h-6 w-6`}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        <div
          className={`${
            isMenuOpen ? "block" : "hidden"
          } sm:hidden bg-[var(--background)]`}
        >
          <div className="pt-2 pb-3 space-y-1">
            <a
              href="#za-koga"
              className="text-[var(--foreground)]/80 hover:text-[var(--foreground)] block pl-3 pr-4 py-2 border-l-4 border-transparent hover:border-primary-500 text-base font-medium transition-colors"
            >
              Kako funkcioniše?
            </a>
            <a
              href="#cene"
              className="text-[var(--foreground)]/80 hover:text-[var(--foreground)] block pl-3 pr-4 py-2 border-l-4 border-transparent hover:border-primary-500 text-base font-medium transition-colors"
            >
              Cene
            </a>
            <a
              href="#footer"
              className="text-[var(--foreground)]/80 hover:text-[var(--foreground)] block pl-3 pr-4 py-2 border-l-4 border-transparent hover:border-primary-500 text-base font-medium transition-colors"
            >
              Kontakt
            </a>
          </div>
          <div className="pt-4 pb-3 border-t border-black/5 dark:border-white/5">
            <div className="flex items-center px-4 space-x-3">
              <button
                onClick={handleLoginClick}
                className="text-[var(--foreground)]/80 hover:text-[var(--foreground)] block px-3 py-2 rounded-md text-base font-medium transition-colors cursor-pointer"
              >
                Prijava
              </button>
            </div>
          </div>
        </div>
      </nav>

      {showLoginForm && <LoginForm onClose={() => setShowLoginForm(false)} />}
    </>
  );
}

export default Nav;
