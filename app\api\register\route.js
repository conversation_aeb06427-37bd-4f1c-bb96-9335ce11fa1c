// app/api/register/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";
import bcrypt from "bcryptjs";
import slugify from "slugify";
import { registerRateLimit } from "../../middleware/redisRateLimit";

export async function POST(request) {
  // Rate limiting check
  const ip =
    request.ip ||
    request.headers.get("x-forwarded-for")?.split(",")[0] ||
    request.headers.get("x-real-ip") ||
    "unknown";

  // Apply rate limiting
  try {
    const rateLimitResult = await registerRateLimit(ip);

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: "Previše pokušaja registracije",
          message: `<PERSON>st<PERSON><PERSON> ste maksimalan broj pokušaja registracije. Pokušajte ponovo za ${Math.ceil(
            rateLimitResult.retryAfter / 60
          )} minuta.`,
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": "3",
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": new Date(
              rateLimitResult.resetTime
            ).toISOString(),
            "Retry-After": rateLimitResult.retryAfter.toString(),
          },
        }
      );
    }
  } catch (rateLimitError) {
    console.error("Rate limit error:", rateLimitError);
    // Nastavi sa normalnim procesom ako rate limiting ne radi
  }

  await dbConnect();

  const body = await request.json();

  // Destrukturiramo polja iz body-ja
  const {
    name,
    email,
    password,
    phone,
    businessName,
    businessType,
    plan = "standard",
  } = body;

  if (!name || !email || !password) {
    return NextResponse.json(
      { error: "Unesite ime, email i lozinku." },
      { status: 400 }
    );
  }

  // Provera da li email već postoji
  const existing = await User.findOne({ email: email.toLowerCase().trim() });
  if (existing) {
    return NextResponse.json(
      { error: "Korisnik sa tim email-om već postoji." },
      { status: 400 }
    );
  }

  // Hash lozinke
  const salt = await bcrypt.genSalt(10);
  const hashed = await bcrypt.hash(password, salt);

  try {
    const defaultWorkingHours = {
      ponedeljak: { from: "09:00", to: "17:00", breaks: [] },
      utorak: { from: "09:00", to: "17:00", breaks: [] },
      sreda: { from: "09:00", to: "17:00", breaks: [] },
      cetvrtak: { from: "09:00", to: "17:00", breaks: [] },
      petak: { from: "09:00", to: "17:00", breaks: [] },
      subota: { from: "10:00", to: "14:00", breaks: [] },
      nedelja: { closed: true, breaks: [] },
    };

    const slug = slugify(businessName || name, { lower: true, strict: true });

    const newUser = await User.create({
      name,
      email: email.toLowerCase().trim(),
      password: hashed,
      role: "user",
      phone,
      businessName,
      businessType,
      plan,
      workingHours: defaultWorkingHours,
      createdAt: new Date(),
      urlSlug: slug,
    });

    // Vraćamo minimalno
    return NextResponse.json(
      {
        message: "Korisnik uspešno kreiran",
        user: { id: newUser._id, email: newUser.email },
      },
      { status: 201 }
    );
  } catch (err) {
    console.error("Register API error:", err);
    return NextResponse.json(
      { error: "Greška na serveru prilikom kreiranja korisnika." },
      { status: 500 }
    );
  }
}
