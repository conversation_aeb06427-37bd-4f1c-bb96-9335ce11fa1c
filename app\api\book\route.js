// app/api/book/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";
import Booking from "../../auth/models/Booking";
import { canCreateBooking } from "../../utils/planLimits";

export async function POST(request) {
  await dbConnect();

  const body = await request.json();
  const { userId, serviceId, name, email, phone, date, time } = body;

  if (!userId || !name || !phone || !date || !time) {
    return NextResponse.json(
      { error: "Svi podaci su obavezni." },
      { status: 400 }
    );
  }

  try {
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Salon nije pronađen." },
        { status: 404 }
      );
    }

    // Pronađi uslugu
    let service = null;

    if (serviceId && Array.isArray(user.services)) {
      service = user.services.find((s) => s._id.toString() === serviceId);
    }

    // Radni dan i radno vreme
    const bookingDate = new Date(date);
    const dayOfWeek = bookingDate.getDay();
    const dayMapping = [
      "nedelja",
      "ponedeljak",
      "utorak",
      "sreda",
      "cetvrtak",
      "petak",
      "subota",
    ];
    const dayKey = dayMapping[dayOfWeek];

    const daySchedule = user.workingHours?.[dayKey];
    if (!daySchedule) {
      return NextResponse.json(
        { error: `Nema radnog vremena za ${dayKey}.` },
        { status: 400 }
      );
    }
    if (daySchedule.closed) {
      return NextResponse.json(
        { error: "Salon je zatvoren tog dana." },
        { status: 400 }
      );
    }

    // Proveri limite plana pre kreiranja booking-a
    const userPlan = user.plan || "standard";

    // Izračunaj broj termina za ovaj mesec
    const today = new Date(date);
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfNextMonth = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      1
    );

    const monthlyBookingsCount = await Booking.countDocuments({
      userId: user._id,
      date: {
        $gte: startOfMonth,
        $lt: startOfNextMonth,
      },
    });

    // Proveri da li može da kreira novi booking (samo mesečni limit)
    const limitCheck = canCreateBooking(
      userPlan,
      0, // dnevni count se ne koristi više
      monthlyBookingsCount
    );

    if (!limitCheck.allowed) {
      return NextResponse.json({ error: limitCheck.reason }, { status: 403 });
    }

    // Kreiraj Date objekat za termin
    const appointmentDate = new Date(`${date}T${time}`);

    // Kreiraj booking koristeći Booking model
    const newBooking = new Booking({
      userId: user._id,
      clientName: name,
      clientPhone: phone,
      serviceName: service?.name || "Opšta usluga",
      date: appointmentDate, // Koristi Date objekat za date polje
      time, // Zadrži string vreme
      status: "pending", // Inicijalni status
    });

    // Sačuvaj booking u bazu
    const savedBooking = await newBooking.save();

    console.log(
      `Zakazivanje uspešno: korisnik ${user.email}, termin ${date} ${time}`
    );

    return NextResponse.json(
      {
        success: true,
        message: "Termin uspešno zakazan",
        bookingId: savedBooking._id,
      },
      { status: 201 }
    );
  } catch (err) {
    console.error("Greška pri zakazivanju:", err);
    return NextResponse.json(
      { error: "Greška pri zakazivanju. Pokušajte ponovo." },
      { status: 500 }
    );
  }
}
