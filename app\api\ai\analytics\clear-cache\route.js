import { NextResponse } from "next/server";
import dbConnect from "../../../../auth/lib/mongoose";
import AIAnalyticsCache from "../../../../auth/models/AIAnalyticsCache";

// Endpoint za brisanje cache-a (samo za testiranje)
export async function POST(request) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "userId je obavezan" },
        { status: 400 }
      );
    }

    await dbConnect();

    // Obriši sav cache za korisnika
    const result = await AIAnalyticsCache.deleteMany({ userId });

    return NextResponse.json({
      success: true,
      message: `Obrisano ${result.deletedCount} cache unosa`,
      deletedCount: result.deletedCount,
    });
  } catch (error) {
    console.error("Clear cache error:", error);
    return NextResponse.json(
      { error: "Greška pri brisanju cache-a" },
      { status: 500 }
    );
  }
}
