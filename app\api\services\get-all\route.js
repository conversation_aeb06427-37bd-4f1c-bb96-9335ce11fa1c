// app/api/services/get-all/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";

export async function GET(request) {
  await dbConnect();

  // Get the URL object to extract search params
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");

  if (!userId) {
    return NextResponse.json(
      { error: "Nedostaje ID korisnika" },
      { status: 400 }
    );
  }

  try {
    // Pronađi korisnika i vrati samo services polje
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Vrati services niz ili prazan niz ako ne postoji
    const services = user.services || [];

    return NextResponse.json({
      services: services,
    });
  } catch (error) {
    console.error("Error retrieving services:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
