import { NextResponse } from "next/server";
import dbConnect from "../../../../auth/lib/mongoose";
import AIAnalyticsCache from "../../../../auth/models/AIAnalyticsCache";

// Endpoint za brisanje svih cache-a (samo za testiranje)
export async function POST(request) {
  try {
    await dbConnect();

    // Obriši sav cache
    const result = await AIAnalyticsCache.deleteMany({});

    return NextResponse.json({
      success: true,
      message: `Obrisano ${result.deletedCount} cache unosa`,
      deletedCount: result.deletedCount,
    });
  } catch (error) {
    console.error("Clear all cache error:", error);
    return NextResponse.json(
      { error: "Greška pri brisanju cache-a" },
      { status: 500 }
    );
  }
}
