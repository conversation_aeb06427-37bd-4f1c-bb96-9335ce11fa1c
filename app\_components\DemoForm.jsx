"use client";

import { useState } from "react";
import PolitikaPrivatnosti from "./PolitikaPrivatnosti";
import UsloviKoriscenja from "./<PERSON>loviKoriscenja";

function DemoForm({ onClose, selectedPlan }) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    businessType: "",
    businessName: "",
    preferredTime: "",
    message: "",
    selectedPlan: selectedPlan || "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          type: "demo",
        }),
      });

      const result = await response.json();

      if (response.ok) {
        console.log("Demo request submitted successfully:", result);
        setIsSubmitted(true);

        // Zatvaramo formu nakon 3 sekunde
        setTimeout(() => {
          onClose();
        }, 3000);
      } else {
        throw new Error(result.error || "Došlo je do greške");
      }
    } catch (error) {
      console.error("Error submitting demo request:", error);
      alert(
        "Došlo je do greške prilikom slanja zahteva. Molimo pokušajte ponovo ili nas kontaktirajte direktno na +381695338913."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in-up">
        <div className="bg-[var(--surface)] rounded-2xl p-8 max-w-md w-full shadow-2xl border border-black/5 dark:border-white/10 text-center animate-fade-in-up animation-delay-200">
          <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-[var(--foreground)] mb-2">
            Demo zakazan!
          </h3>
          <p className="text-[var(--foreground)]/70 mb-4">
            Hvala vam na interesovanju! Kontaktiraćemo vas u najkraćem roku da
            dogovorimo termin za demo prezentaciju.
          </p>
          <div className="text-sm text-teal-600 dark:text-teal-400 font-medium">
            Zatvaramo formu...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in-up">
      <div className="bg-[var(--surface)] rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-black/5 dark:border-white/10 animate-fade-in-up animation-delay-200">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-3xl font-bold text-[var(--foreground)] bg-gradient-to-r from-teal-500 to-blue-600 bg-clip-text text-transparent">
              {selectedPlan
                ? `Zainteresovani ste za ${selectedPlan} plan`
                : "Zakažite demo"}
            </h2>
            <p className="text-[var(--foreground)]/70 mt-2">
              {selectedPlan
                ? `Popunite formu i kontaktiraćemo Vas u vezi sa ${selectedPlan} planom`
                : "Vidite kako Zakaži AI može transformisati vaš biznis"}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-[var(--surface-hover)] rounded-full transition-colors"
          >
            <svg
              className="w-6 h-6 text-[var(--foreground)]/70"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Demo highlights */}
        {!selectedPlan && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 mb-6 border border-blue-200/50 dark:border-blue-700/50">
            <h3 className="text-lg font-semibold text-[var(--foreground)] mb-3 flex items-center">
              <svg
                className="w-5 h-5 text-blue-500 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
              Šta ćete videti u demo prezentaciji:
            </h3>
            <ul className="space-y-2 text-[var(--foreground)]/80">
              <li className="flex items-center">
                <span className="text-blue-500 mr-2">✓</span>
                Kako AI agent automatski zakazuje termine
              </li>
              <li className="flex items-center">
                <span className="text-blue-500 mr-2">✓</span>
                Pregled kontrolne table i analitike
              </li>
              <li className="flex items-center">
                <span className="text-blue-500 mr-2">✓</span>
                Integracija sa Vašim postojećim sistemom
              </li>
              <li className="flex items-center">
                <span className="text-blue-500 mr-2">✓</span>
                Prilagođavanje prema vašim potrebama
              </li>
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                <strong>Personalizovana prezentacija za Vaš biznis</strong>
              </li>
            </ul>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {selectedPlan && (
            <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-xl p-4 border border-teal-200/50 dark:border-teal-700/50">
              <label className="block text-sm font-medium text-[var(--foreground)] mb-2">
                Odabrani plan
              </label>
              <div className="flex items-center">
                <span className="text-lg font-semibold text-teal-600 dark:text-teal-400">
                  {selectedPlan}
                </span>
                <svg
                  className="w-5 h-5 text-teal-500 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-[var(--foreground)] mb-2"
              >
                Ime i prezime *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="Vaše ime i prezime"
              />
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-[var(--foreground)] mb-2"
              >
                Email adresa *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-[var(--foreground)] mb-2"
              >
                Broj telefona *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                required
                value={formData.phone}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="+381 60 123 4567"
              />
            </div>

            <div>
              <label
                htmlFor="businessType"
                className="block text-sm font-medium text-[var(--foreground)] mb-2"
              >
                Tip biznisa *
              </label>
              <select
                id="businessType"
                name="businessType"
                required
                value={formData.businessType}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                <option value="">Izaberite tip biznisa</option>
                <option value="salon">Frizerski salon</option>
                <option value="beauty">Salon lepote</option>
                <option value="spa">Spa centar</option>
                <option value="massage">Masažni salon</option>
                <option value="nails">Salon za nokte</option>
                <option value="barbershop">Berberin</option>
                <option value="dental">Stomatološka ordinacija</option>
                <option value="medical">Medicinska ordinacija</option>
                <option value="fitness">Fitnes centar</option>
                <option value="restaurant">Restoran</option>
                <option value="other">Ostalo</option>
              </select>
            </div>
          </div>

          <div>
            <label
              htmlFor="businessName"
              className="block text-sm font-medium text-[var(--foreground)] mb-2"
            >
              Naziv biznisa *
            </label>
            <input
              type="text"
              id="businessName"
              name="businessName"
              required
              value={formData.businessName}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              placeholder="Naziv Vašeg salona/biznisa"
            />
          </div>

          <div>
            <label
              htmlFor="preferredTime"
              className="block text-sm font-medium text-[var(--foreground)] mb-2"
            >
              Preferirano vreme za demo
            </label>
            <select
              id="preferredTime"
              name="preferredTime"
              value={formData.preferredTime}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            >
              <option value="">Izaberite preferirano vreme</option>
              <option value="morning">Ujutru (9:00 - 12:00)</option>
              <option value="afternoon">Popodne (12:00 - 17:00)</option>
              <option value="evening">Uveče (17:00 - 20:00)</option>
              <option value="flexible">Fleksibilno</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="message"
              className="block text-sm font-medium text-[var(--foreground)] mb-2"
            >
              Dodatne informacije (opciono)
            </label>
            <textarea
              id="message"
              name="message"
              rows={4}
              value={formData.message}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
              placeholder="Recite nam nešto više o vašim potrebama ili specifičnim pitanjima..."
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-6 py-3 border border-black/20 dark:border-white/20 text-[var(--foreground)] rounded-lg hover:bg-[var(--surface-hover)] transition-all duration-300"
            >
              Otkaži
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg font-medium transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Šalje se...
                </span>
              ) : (
                "Zakažite demo"
              )}
            </button>
          </div>
        </form>
        <div className="mt-6 text-center text-sm text-[var(--foreground)]/60">
          <div>
            Klikom na "Pošaljite zahtev" slažete se sa našim{" "}
            <span className="inline">
              <UsloviKoriscenja />
            </span>{" "}
            i{" "}
            <span className="inline">
              <PolitikaPrivatnosti />
            </span>
            .
          </div>
        </div>
      </div>
    </div>
  );
}

export default DemoForm;
