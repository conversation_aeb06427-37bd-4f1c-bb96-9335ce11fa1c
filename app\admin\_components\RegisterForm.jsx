"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../contexts/AuthProvider";

export default function RegisterForm({ onClose, isAdminCreating = false }) {
  const router = useRouter();
  const { register } = useAuth();
  const [form, setForm] = useState({
    name: "",
    email: "",
    password: "",
    phone: "",
    businessName: "",
    businessType: "",
    plan: "standard",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);
    try {
      await register(
        form.name,
        form.email,
        form.password,
        form.phone,
        form.businessName,
        form.businessType,
        form.plan
      );
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-[var(--surface)] p-8 rounded-lg shadow-xl max-w-md w-full relative overflow-y-auto max-h-[90vh]">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-[var(--foreground)]/70 hover:text-[var(--foreground)]"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <h2 className="text-2xl font-bold mb-6 text-[var(--foreground)]">
          {isAdminCreating ? "Registracija novog korisnika" : "Registracija"}
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <form
          onSubmit={handleSubmit}
          className="max-w-md mx-auto p-4 space-y-4 bg-black rounded shadow"
        >
          <input
            name="name"
            placeholder="Name"
            value={form.name}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
          />
          <input
            name="email"
            type="email"
            placeholder="Email"
            value={form.email}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
          />
          <input
            name="password"
            type="password"
            placeholder="Password"
            value={form.password}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
          />
          <input
            name="phone"
            placeholder="Phone"
            value={form.phone}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
          />
          <input
            name="businessName"
            placeholder="Business Name"
            value={form.businessName}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
          />
          <input
            name="businessType"
            placeholder="Business Type"
            value={form.businessType}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
          ></input>
          <select
            name="plan"
            value={form.plan}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)] text-[var(--foreground)]"
          >
            <option value="standard">Standard - 2.000 RSD/mesečno</option>
            <option value="standard-plus">
              Standard Plus - 2.800 RSD/mesečno
            </option>
            <option value="premium">Premium - 4.500 RSD/mesečno</option>
            <option value="enterprise">Enterprise - Po proceni</option>
          </select>
          <button
            type="submit"
            disabled={loading}
            className="w-full p-2 bg-blue-600 text-white rounded"
          >
            {loading ? "Registering..." : "Register"}
          </button>
        </form>
      </div>
    </div>
  );
}

{
  /*   <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-[var(--foreground)]/70 mb-1"
            >
              Ime i prezime
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="email"
              className="block text-sm font-medium text-[var(--foreground)]/70 mb-1"
            >
              Email adresa
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="password"
              className="block text-sm font-medium text-[var(--foreground)]/70 mb-1"
            >
              Lozinka
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-[var(--foreground)]/70 mb-1"
            >
              Potvrdite lozinku
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="businessName"
              className="block text-sm font-medium text-[var(--foreground)]/70 mb-1"
            >
              Naziv biznisa
            </label>
            <input
              type="text"
              id="businessName"
              name="businessName"
              value={formData.businessName}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
              required
            />
          </div>

          <div className="mb-6">
            <label
              htmlFor="businessType"
              className="block text-sm font-medium text-[var(--foreground)]/70 mb-1"
            >
              Tip biznisa
            </label>
            <select
              id="businessType"
              name="businessType"
              value={formData.businessType}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-[var(--foreground)]/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[var(--background)]"
              required
            >
              <option value="salon">Frzerski Salon</option>
              <option value="spa">Spa/Masaza centar</option>
              <option value="medical">Manikir/Pedikir</option>
              <option value="fitness">Restoran</option>
              <option value="other">Drugo</option>
            </select>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-2 px-4 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {isLoading
              ? "Registracija u toku..."
              : isAdminCreating
              ? "Registruj korisnika"
              : "Registruj se"}
          </button>
        </form> */
}
