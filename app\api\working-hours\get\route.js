// app/api/working-hours/get/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";

export async function GET(request) {
  await dbConnect();

  // Get the URL object to extract search params
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");

  if (!userId) {
    return NextResponse.json(
      { error: "Nedostaje ID korisnika" },
      { status: 400 }
    );
  }

  try {
    // Pronađi korisnika i vrati samo workingHours polje
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Vrati workingHours ili prazan objekat ako ne postoji
    const workingHours = user.workingHours || {};

    return NextResponse.json({
      workingHours: workingHours,
    });
  } catch (error) {
    console.error("Error retrieving working hours:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
