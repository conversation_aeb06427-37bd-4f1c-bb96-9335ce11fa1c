import mongoose from "mongoose";

const PlanChangeRequestSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  businessName: {
    type: String,
    default: "",
  },
  currentPlan: {
    type: String,
    required: true,
  },
  requestedPlan: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: "plan-change",
  },
  status: {
    type: String,
    enum: ["novo", "kontaktirano", "završeno"],
    default: "novo",
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
});

// Dodaje virtuelno polje za ID kao string (kompatibilnost sa postojećim kodom)
PlanChangeRequestSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

PlanChangeRequestSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id.toString();
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

export default mongoose.models.PlanChangeRequest || mongoose.model("PlanChangeRequest", PlanChangeRequestSchema);
