import rateLimit from 'express-rate-limit';

// Osnovni rate limiter za sve API pozive
export const basicRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minuta
  max: 100, // maksimalno 100 zahteva po IP adresi u 15 minuta
  message: {
    error: 'Previše zahteva sa ove IP adrese. Pokušajte ponovo za 15 minuta.',
    retryAfter: 15 * 60 // sekunde
  },
  standardHeaders: true, // Vraća rate limit info u `RateLimit-*` headers
  legacyHeaders: false, // Onemogućava `X-RateLimit-*` headers
  keyGenerator: (req) => {
    // Koristi IP adresu kao ključ
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  },
  skip: (req) => {
    // Preskače rate limiting za localhost u development modu
    if (process.env.NODE_ENV === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
      return true;
    }
    return false;
  }
});

// Strožiji rate limiter za autentifikaciju
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minuta
  max: 5, // maksimalno 5 pokušaja prijave po IP adresi u 15 minuta
  message: {
    error: 'Previše pokušaja prijave. Pokušajte ponovo za 15 minuta.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Kombinuje IP adresu i email za preciznije ograničavanje
    const ip = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    const email = req.body?.email || '';
    return `${ip}:${email}`;
  },
  skip: (req) => {
    if (process.env.NODE_ENV === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
      return true;
    }
    return false;
  }
});

// Rate limiter za registraciju
export const registerRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 sat
  max: 3, // maksimalno 3 registracije po IP adresi u 1 sat
  message: {
    error: 'Previše pokušaja registracije. Pokušajte ponovo za 1 sat.',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  },
  skip: (req) => {
    if (process.env.NODE_ENV === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
      return true;
    }
    return false;
  }
});

// Rate limiter za slanje email-ova
export const emailRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 sat
  max: 10, // maksimalno 10 email-ova po IP adresi u 1 sat
  message: {
    error: 'Previše email zahteva. Pokušajte ponovo za 1 sat.',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  },
  skip: (req) => {
    if (process.env.NODE_ENV === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
      return true;
    }
    return false;
  }
});

// Rate limiter za booking API
export const bookingRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minut
  max: 10, // maksimalno 10 booking zahteva po IP adresi u 1 minut
  message: {
    error: 'Previše zahteva za zakazivanje. Pokušajte ponovo za 1 minut.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  },
  skip: (req) => {
    if (process.env.NODE_ENV === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
      return true;
    }
    return false;
  }
});

// Rate limiter za AI API pozive
export const aiRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minut
  max: 20, // maksimalno 20 AI zahteva po IP adresi u 1 minut
  message: {
    error: 'Previše AI zahteva. Pokušajte ponovo za 1 minut.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  },
  skip: (req) => {
    if (process.env.NODE_ENV === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
      return true;
    }
    return false;
  }
});

// Funkcija za kreiranje custom rate limitera
export function createCustomRateLimit(options = {}) {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minuta
    max: 100,
    message: {
      error: 'Previše zahteva. Pokušajte ponovo kasnije.',
      retryAfter: 15 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    },
    skip: (req) => {
      if (process.env.NODE_ENV === 'development' && 
          (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost')) {
        return true;
      }
      return false;
    }
  };

  return rateLimit({ ...defaultOptions, ...options });
}
