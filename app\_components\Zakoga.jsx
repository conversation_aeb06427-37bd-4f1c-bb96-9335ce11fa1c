"use client";

import React, { useState } from "react";
import { useIntersectionObserver } from "../hooks/useIntersectionObserver";
import DemoForm from "./DemoForm";

function Zakoga() {
  const [ref, isIntersecting, hasIntersected] = useIntersectionObserver();
  const [showDemoForm, setShowDemoForm] = useState(false);

  const handleDemoClick = (e) => {
    e.preventDefault();
    setShowDemoForm(true);
  };
  const processSteps = [
    {
      step: "1",
      title: "Klijent pozove Vaš biznis",
      description:
        "<PERSON><PERSON><PERSON><PERSON> pokušava da Vas pozove da zakaze termin, ali se niko ne javlja jer ste zauzeti sa drugim klijentima ili je van radnog vremena.",
      icon: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      ),
      color: "from-red-500 to-orange-500",
      bgColor: "bg-red-50 dark:bg-red-900/20",
    },
    {
      step: "2",
      title: "Automatska SMS poruka",
      description:
        "Klijent automatski dobija SMS poruku sa linkom koji ga direktno vodi na našu formu za zakazivanje termina - bez čekanja, bez frustracije.",
      icon: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
        />
      ),
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
    },
    {
      step: "3",
      title: "AI agent pomaže u pronalaženju termina",
      description:
        "Naš napredni AI agent analizira Vaš raspored u realnom vremenu i predlaže klijentu najbolje dostupne termine koji odgovaraju i njemu i Vama.",
      icon: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
        />
      ),
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
    },
    {
      step: "4",
      title: "Klijent bira i potvrđuje termin",
      description:
        "Klijent jednostavno bira željeni termin iz dostupnih opcija i potvrđuje rezervaciju - sve se dešava za manje od 2 minuta.",
      icon: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      ),
      color: "from-blue-500 to-indigo-500",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
    },
    {
      step: "5",
      title: "Termin se pojavljuje u Vašem sistemu",
      description:
        "Zakazani termin se automatski pojavljuje na profilu Vašeg biznisa sa tačnim datumom, vremenom i informacijama o klijentu - bez Vašeg angažovanja.",
      icon: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
        />
      ),
      color: "from-teal-500 to-blue-500",
      bgColor: "bg-teal-50 dark:bg-teal-900/20",
    },
    {
      step: "6",
      title: "Zadovoljni korisnici",
      description:
        "Vaši klijenti su zadovoljni brzim i jednostavnim zakazivanjem, a Vi imate potpunu kontrolu nad svojim rasporedom i više vremena za ono što je važno.",
      icon: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      ),
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50 dark:bg-green-900/20",
    },
  ];

  return (
    <section
      ref={ref}
      id="za-koga"
      className="py-16 sm:py-24 bg-[var(--muted)] relative overflow-hidden"
    >
      {/* Booking-related decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-10 w-32 h-32 opacity-40 animate-float">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-purple-400"
          >
            <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>

        <div className="absolute bottom-10 left-10 w-28 h-28 opacity-40 animate-float animation-delay-2000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-pink-500"
          >
            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
          </svg>
        </div>

        <div className="absolute top-1/4 left-10 w-20 h-20 opacity-40 animate-float animation-delay-1000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-teal-400"
          >
            <path d="M3 3h18v18H3z M8 6v6c0 1 1 2 2 2h4c1 0 2-1 2-2V6M12 12v6M8 18h8 M6 10h2M16 10h2" />
          </svg>
        </div>

        <div className="absolute bottom-1/4 right-30 w-26 h-26 opacity-40 animate-float animation-delay-3000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-green-400"
          >
            <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
          </svg>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div
          className={`lg:text-center transition-all duration-1000 ${
            hasIntersected ? "animate-fade-in-up" : "opacity-0 translate-y-10"
          }`}
        >
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-[var(--foreground)] sm:text-4xl">
            Kako funkcioniše Zakaži AI?
          </p>
          <p
            className={`mt-4 max-w-3xl text-xl text-[var(--foreground)]/70 lg:mx-auto transition-all duration-1000 delay-200 ${
              hasIntersected ? "animate-fade-in-up" : "opacity-0 translate-y-10"
            }`}
          >
            Jednostavan proces u 6 koraka koji automatski pretvara propuštene
            pozive u zakazane termine
          </p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {processSteps.map((step, index) => (
              <div
                className={`transition-all duration-1000 ${
                  hasIntersected
                    ? "animate-fade-in-up"
                    : "opacity-0 translate-y-10"
                }`}
                key={index}
                style={{
                  transitionDelay: hasIntersected
                    ? `${(index + 2) * 200}ms`
                    : "0ms",
                }}
              >
                <div
                  className={`group relative overflow-hidden rounded-2xl ${step.bgColor} p-8 transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 border border-transparent hover:border-opacity-50 h-full flex flex-col`}
                >
                  {/* Step number */}
                  <div className="absolute top-4 right-4">
                    <div
                      className={`flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r ${step.color} text-white font-bold text-lg shadow-lg`}
                    >
                      {step.step}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="mb-6">
                    <div
                      className={`inline-flex items-center justify-center p-4 bg-gradient-to-r ${step.color} rounded-xl shadow-lg group-hover:shadow-2xl group-hover:scale-110 transition-all duration-500`}
                    >
                      <svg
                        className="h-8 w-8 text-white transition-transform duration-300 group-hover:rotate-12"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        {step.icon}
                      </svg>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-grow">
                    <h3 className="text-xl font-bold text-[var(--foreground)] mb-4 group-hover:text-opacity-90 transition-colors duration-300">
                      {step.title}
                    </h3>
                    <p className="text-[var(--foreground)]/70 leading-relaxed group-hover:text-[var(--foreground)]/90 transition-colors duration-300">
                      {step.description}
                    </p>
                  </div>

                  {/* Connecting line for larger screens */}
                  {index < processSteps.length - 1 && index % 2 === 0 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-gray-300 to-transparent transform -translate-y-1/2"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Final CTA Section */}
        <div
          className={`text-center mt-20 transition-all duration-1000 delay-1000 ${
            hasIntersected ? "animate-fade-in-up" : "opacity-0 translate-y-10"
          }`}
        >
          <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-2xl p-8 mb-8">
            <h3 className="text-2xl font-bold text-[var(--foreground)] mb-4">
              Idealno za sve biznise koji rade sa terminima
            </h3>
            <p className="text-lg text-[var(--foreground)]/70 mb-6 max-w-4xl mx-auto">
              <span className="font-semibold text-teal-600 dark:text-teal-400">
                Frizerski saloni
              </span>
              ,
              <span className="font-semibold text-blue-600 dark:text-blue-400">
                {" "}
                kozmetički saloni
              </span>
              ,
              <span className="font-semibold text-purple-600 dark:text-purple-400">
                {" "}
                spa centri
              </span>
              ,
              <span className="font-semibold text-green-600 dark:text-green-400">
                {" "}
                restorani
              </span>
              ,
              <span className="font-semibold text-pink-600 dark:text-pink-400">
                {" "}
                hoteli
              </span>
              ,
              <span className="font-semibold text-orange-600 dark:text-orange-400">
                {" "}
                ordinacije
              </span>{" "}
              - svi biznisi koji rade po principu zakazivanja, termina i
              rezervacija mogu odmah da unaprede svoje poslovanje uz Zakaži AI.
            </p>
          </div>

          <button
            onClick={handleDemoClick}
            className="group inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-full text-white bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 shadow-2xl hover:shadow-3xl transform transition-all duration-500 hover:-translate-y-2 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 relative overflow-hidden"
          >
            <span className="relative z-10">Želim demo za moj biznis</span>
            <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-blue-700 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
            <svg
              className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300 relative z-10"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              />
            </svg>
          </button>
        </div>
      </div>

      {showDemoForm && <DemoForm onClose={() => setShowDemoForm(false)} />}
    </section>
  );
}

export default Zakoga;
