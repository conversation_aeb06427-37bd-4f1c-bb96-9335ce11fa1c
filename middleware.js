import { NextResponse } from "next/server";

// In-memory store za rate limiting (u produkciji koristiti Redis)
const rateLimitStore = new Map();

// Funkcija za čišćenje starih unosa
function cleanupOldEntries() {
  const now = Date.now();
  for (const [key, data] of rateLimitStore.entries()) {
    if (now > data.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Glavna rate limiting funkcija
function rateLimit(ip, endpoint, limits) {
  cleanupOldEntries();

  const key = `${ip}:${endpoint}`;
  const now = Date.now();

  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + limits.windowMs,
      firstRequest: now,
    });
    return {
      allowed: true,
      remaining: limits.max - 1,
      resetTime: now + limits.windowMs,
    };
  }

  const data = rateLimitStore.get(key);

  if (now > data.resetTime) {
    // Reset window
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + limits.windowMs,
      firstRequest: now,
    });
    return {
      allowed: true,
      remaining: limits.max - 1,
      resetTime: now + limits.windowMs,
    };
  }

  if (data.count >= limits.max) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: data.resetTime,
      retryAfter: Math.ceil((data.resetTime - now) / 1000),
    };
  }

  data.count++;
  rateLimitStore.set(key, data);

  return {
    allowed: true,
    remaining: limits.max - data.count,
    resetTime: data.resetTime,
  };
}

// Rate limiting konfiguracije - SAMO za kritične endpoint-e
const rateLimitConfigs = {
  // KRITIČNI ENDPOINT-I (strogi limiti)
  "/api/login": { max: 5, windowMs: 15 * 60 * 1000 }, // 5 pokušaja u 15 minuta
  "/api/register": { max: 3, windowMs: 60 * 60 * 1000 }, // 3 pokušaja u 1 sat
  "/api/send-email": { max: 10, windowMs: 60 * 60 * 1000 }, // 10 email-ova u 1 sat
  "/api/contact": { max: 5, windowMs: 60 * 60 * 1000 }, // 5 kontakt forma u 1 sat
  "/api/book": { max: 10, windowMs: 60 * 1000 }, // 10 booking-a u 1 minut
  "/api/schedule": { max: 15, windowMs: 60 * 1000 }, // 15 schedule zahteva u 1 minut

  // DASHBOARD ENDPOINT-I (veći limiti)
  "/api/bookings/get-all": { max: 100, windowMs: 60 * 1000 }, // 100 zahteva u 1 minut
  "/api/services/get-all": { max: 100, windowMs: 60 * 1000 }, // 100 zahteva u 1 minut
  "/api/me": { max: 200, windowMs: 60 * 1000 }, // 200 zahteva u 1 minut
  "/api/users/profile": { max: 50, windowMs: 60 * 1000 }, // 50 zahteva u 1 minut

  // AI ENDPOINT-I (umeren limit)
  "/api/ai": { max: 30, windowMs: 60 * 1000 }, // 30 AI zahteva u 1 minut
};

export function middleware(request) {
  // Proverava da li je zahtev za API
  if (!request.nextUrl.pathname.startsWith("/api/")) {
    return NextResponse.next();
  }

  // Preskače rate limiting u development modu za localhost (ONEMOGUĆENO ZA TESTIRANJE)
  // if (process.env.NODE_ENV === "development") {
  //   const ip =
  //     request.ip || request.headers.get("x-forwarded-for") || "localhost";
  //   if (
  //     ip === "127.0.0.1" ||
  //     ip === "::1" ||
  //     ip === "localhost" ||
  //     ip.includes("localhost")
  //   ) {
  //     return NextResponse.next();
  //   }
  // }

  // Dobija IP adresu
  const ip =
    request.ip ||
    request.headers.get("x-forwarded-for")?.split(",")[0] ||
    request.headers.get("x-real-ip") ||
    "unknown";

  // Dobija endpoint
  const endpoint = request.nextUrl.pathname;

  // Pronalazi odgovarajuću konfiguraciju - SAMO za definisane endpoint-e
  let config = null;
  for (const [pattern, limits] of Object.entries(rateLimitConfigs)) {
    if (endpoint.startsWith(pattern)) {
      config = limits;
      break;
    }
  }

  // Ako endpoint NIJE u listi, PRESKAČE rate limiting
  if (!config) {
    return NextResponse.next();
  }

  // Primenjuje rate limiting SAMO na definisane endpoint-e
  const result = rateLimit(ip, endpoint, config);

  if (!result.allowed) {
    // Rate limit exceeded
    const response = NextResponse.json(
      {
        error: "Previše zahteva",
        message:
          "Dostigli ste maksimalan broj zahteva. Pokušajte ponovo kasnije.",
        retryAfter: result.retryAfter,
      },
      { status: 429 }
    );

    // Dodaje rate limit headers
    response.headers.set("X-RateLimit-Limit", config.max.toString());
    response.headers.set("X-RateLimit-Remaining", "0");
    response.headers.set(
      "X-RateLimit-Reset",
      new Date(result.resetTime).toISOString()
    );
    response.headers.set("Retry-After", result.retryAfter.toString());

    return response;
  }

  // Zahtev je dozvoljen
  const response = NextResponse.next();

  // Dodaje rate limit headers
  response.headers.set("X-RateLimit-Limit", config.max.toString());
  response.headers.set("X-RateLimit-Remaining", result.remaining.toString());
  response.headers.set(
    "X-RateLimit-Reset",
    new Date(result.resetTime).toISOString()
  );

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
