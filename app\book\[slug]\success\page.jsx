"use client";

import { useEffect, useState, use } from "react";

export default function BookingSuccessPage({ params }) {
  // Safely extract the slug using React.use()
  const unwrappedParams = use(params);
  const slug = unwrappedParams?.slug || "";

  const [businessName, setBusinessName] = useState("");

  useEffect(() => {
    if (!slug) return; // Only proceed if we have a slug

    const fetchUserData = async () => {
      try {
        const response = await fetch(`/api/users/get-by-slug?slug=${slug}`);
        if (response.ok) {
          const data = await response.json();
          setBusinessName(data.user?.businessName || data.user?.name || "");
        }
      } catch (error) {
        console.error("Error fetching salon data:", error);
      }
    };

    fetchUserData();
  }, [slug]);

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md mt-10">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 mb-6 bg-green-100 rounded-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-8 h-8 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>

        <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
          Vaš termin je uspešno zakazan!
        </h2>

        {businessName && (
          <p className="text-lg mb-6 text-gray-600 dark:text-gray-300">
            Salon <span className="font-semibold">{businessName}</span> je
            primio vašu rezervaciju.
          </p>
        )}

        <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mb-6 text-left">
          <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">
            Šta dalje?
          </h3>
          <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-2">
            <li>Pripremite se za dogovoreni termin!</li>
            <li>Vidimo se!</li>
          </ul>
        </div>

        <div className="space-x-4">
          <h3 className="inline-block px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition duration-200">
            Hvala na zakazivanju!
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 p-4">
            Možete napustiti stranicu
          </p>
        </div>
      </div>
    </div>
  );
}
