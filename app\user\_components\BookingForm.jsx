"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/contexts/AuthProvider";

export default function BookingForm({ user, services, onComplete }) {
  const { refreshUserData } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    serviceId: "",
    date: "",
    time: "",
  });

  // Update serviceId when services are loaded or changed
  useEffect(() => {
    if (services && services.length > 0 && !formData.serviceId) {
      setFormData((prev) => ({
        ...prev,
        serviceId: services[0]._id,
      }));
    }
  }, [services]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [selectedDayName, setSelectedDayName] = useState("");

  // Prevođenje imena dana na srpski
  const dayNameMapping = {
    0: "nedelja",
    1: "ponedeljak",
    2: "utorak",
    3: "sreda",
    4: "cetvrtak",
    5: "petak",
    6: "subota",
  };

  // Formatiranje vremena za prikaz
  const formatTime = (timeStr) => {
    if (!timeStr) return "";

    const [hours, minutes] = timeStr.split(":");
    if (!hours || !minutes) return timeStr;

    return `${hours.padStart(2, "0")}:${minutes.padStart(2, "0")}`;
  };

  // Generisanje dostupnih termina na osnovu radnog vremena
  const generateTimeSlotsForDay = (daySchedule) => {
    if (!daySchedule || daySchedule.closed) return [];

    // Provera da li radne vreme (from/to) postoji
    if (!daySchedule.from || !daySchedule.to) return [];

    const slots = [];
    const interval = 30; // 30 minuta između termina

    try {
      const [fromHour, fromMinute] = (daySchedule.from || "9:00")
        .split(":")
        .map(Number);
      const [toHour, toMinute] = (daySchedule.to || "17:00")
        .split(":")
        .map(Number);

      // Provera validnosti vrednosti
      if (
        isNaN(fromHour) ||
        isNaN(fromMinute) ||
        isNaN(toHour) ||
        isNaN(toMinute)
      ) {
        console.error("Nevažeći format radnog vremena:", daySchedule);
        return [];
      }

      // Konvertovanje u minute od početka dana
      const fromMinutes = fromHour * 60 + fromMinute;
      const toMinutes = toHour * 60 + toMinute;

      // Kreiranje vremenskih slotova sa intervalom od 30 minuta
      for (let mins = fromMinutes; mins < toMinutes; mins += interval) {
        const hour = Math.floor(mins / 60);
        const minute = mins % 60;

        const timeStr = `${hour}:${minute === 0 ? "00" : minute}`;

        // Provera da li je vreme u pauzi
        let isInBreak = false;
        if (
          daySchedule.breaks &&
          Array.isArray(daySchedule.breaks) &&
          daySchedule.breaks.length > 0
        ) {
          isInBreak = daySchedule.breaks.some((breakTime) => {
            if (!breakTime || !breakTime.from || !breakTime.to) return false;

            try {
              const [breakFromHour, breakFromMinute] = breakTime.from
                .split(":")
                .map(Number);
              const [breakToHour, breakToMinute] = breakTime.to
                .split(":")
                .map(Number);

              // Provera validnosti vrednosti
              if (
                isNaN(breakFromHour) ||
                isNaN(breakFromMinute) ||
                isNaN(breakToHour) ||
                isNaN(breakToMinute)
              ) {
                return false;
              }

              const breakFromMinutes = breakFromHour * 60 + breakFromMinute;
              const breakToMinutes = breakToHour * 60 + breakToMinute;

              return mins >= breakFromMinutes && mins < breakToMinutes;
            } catch (err) {
              console.error("Greška pri obradi pauze:", err);
              return false;
            }
          });
        }

        if (!isInBreak) {
          slots.push(timeStr);
        }
      }

      return slots;
    } catch (err) {
      console.error("Greška pri generisanju termina:", err);
      return [];
    }
  };

  // Ažuriranje dostupnih termina kada se datum promeni
  useEffect(() => {
    if (formData.date) {
      const date = new Date(formData.date);
      const dayOfWeek = date.getDay(); // 0 = nedelja, 1 = ponedeljak, itd.
      const dayName = dayNameMapping[dayOfWeek];
      setSelectedDayName(dayName);

      // Dohvati workingHours iz ispravne strukture korisnika
      const workingHours = user?.user?.workingHours || user?.workingHours;

      // Provera da li workingHours i raspored za dan postoje
      if (workingHours && workingHours[dayName]) {
        // Dohvati raspored za taj dan iz radnog vremena korisnika
        const daySchedule = workingHours[dayName];
        const timeSlots = generateTimeSlotsForDay(daySchedule);
        setAvailableTimeSlots(timeSlots);

        // Resetuj selektovano vreme ako dan nije radni ili nema dostupnih termina
        if (timeSlots.length === 0) {
          setFormData((prev) => ({ ...prev, time: "" }));
        }
      } else {
        // Ako nema radnog vremena za taj dan, postavi praznu listu
        setAvailableTimeSlots([]);
        setFormData((prev) => ({ ...prev, time: "" }));
      }
    }
  }, [formData.date, user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setMessage("");

    try {
      // Debug logovi uklonjeni za produkciju

      const response = await fetch("/api/book", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          userId: user.user ? user.user._id : user._id, // ID korisnika (salona) kod kojeg se zakazuje
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška prilikom zakazivanja termina");
      }

      // Uspešno zakazivanje
      setMessage("Termin je uspešno zakazan!");
      setFormData({
        name: "",
        phone: "",
        serviceId: services.length > 0 ? services[0]._id : "",
        date: "",
        time: "",
      });

      // Ovde ne pozivamo refreshUserData jer će Appointments komponenta to učiniti

      // Obavesti roditelja o uspešnom zakazivanju
      if (onComplete) {
        onComplete(true);
      }
    } catch (err) {
      console.error("Greška pri zakazivanju:", err);
      setError(err.message);
      if (onComplete) {
        onComplete(false);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="p-2 bg-red-100 text-red-700 rounded-md">{error}</div>
      )}

      {message && (
        <div className="p-2 bg-green-100 text-green-700 rounded-md">
          {message}
        </div>
      )}

      <div>
        <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
          Ime klijenta
        </label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          required
          className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
          placeholder="Unesite ime klijenta"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
          Telefon
        </label>
        <input
          type="tel"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          required
          className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
          placeholder="Npr. 0601234567"
        />
      </div>

      {services.length > 0 && (
        <div>
          <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
            Usluga
          </label>
          <select
            name="serviceId"
            value={formData.serviceId}
            onChange={handleChange}
            required
            className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
          >
            <option value="">— Izaberite uslugu —</option>
            {services.map((service) => (
              <option key={service._id} value={service._id}>
                {service.name} - {service.durationMins} min (
                {service.price ? `${service.price} RSD` : "Cena na upit"})
              </option>
            ))}
          </select>
        </div>
      )}

      <div>
        <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
          Datum
        </label>
        <input
          type="date"
          name="date"
          value={formData.date}
          onChange={handleChange}
          required
          min={new Date().toISOString().split("T")[0]} // Minimalni datum je današnji
          className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
        />
        {formData.date &&
          selectedDayName &&
          (() => {
            const workingHours = user?.user?.workingHours || user?.workingHours;
            if (workingHours && workingHours[selectedDayName]?.closed) {
              return (
                <p className="mt-1 text-sm text-red-600">
                  Izabrani dan je neradni. Molimo vas izaberite drugi datum.
                </p>
              );
            } else if (availableTimeSlots.length === 0) {
              return (
                <p className="mt-1 text-sm text-red-600">
                  Nema dostupnih termina za izabrani datum.
                </p>
              );
            }
            return null;
          })()}
      </div>

      <div>
        <label className="block text-sm font-medium mb-1 text-[var(--foreground)]">
          Vreme
        </label>
        <select
          name="time"
          value={formData.time}
          onChange={handleChange}
          required
          disabled={availableTimeSlots.length === 0}
          className="w-full p-2 border rounded-md bg-[var(--background)] text-[var(--foreground)]"
        >
          <option value="">Izaberite vreme</option>
          {availableTimeSlots.map((time) => (
            <option key={time} value={time}>
              {formatTime(time)}
            </option>
          ))}
        </select>
      </div>

      <div id="artibot-1749675375306"></div>
      <button
        type="submit"
        disabled={loading || availableTimeSlots.length === 0}
        className="w-full py-2 px-4 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md disabled:opacity-50"
      >
        {loading ? "Zakazuje se..." : "Zakaži termin"}
      </button>
    </form>
  );
}
