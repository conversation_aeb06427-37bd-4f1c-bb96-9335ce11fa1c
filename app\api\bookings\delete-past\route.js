import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import Booking from "../../../auth/models/Booking";

export async function DELETE(request) {
  await dbConnect();

  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "User ID je obavezan" },
        { status: 400 }
      );
    }

    // Kreiraj datum za danas (početak dana)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Pronađi sve prošle termine za ovog korisnika
    const pastBookingsQuery = {
      userId: userId,
      date: { $lt: today },
    };

    // Prvo prebrojimo koliko termina će biti obrisano
    const countResult = await Booking.countDocuments(pastBookingsQuery);

    if (countResult === 0) {
      return NextResponse.json(
        { message: "<PERSON><PERSON> prošlih termina za brisanje", deletedCount: 0 },
        { status: 200 }
      );
    }

    // Obriši sve prošle termine
    const deleteResult = await Booking.deleteMany(pastBookingsQuery);

    return NextResponse.json(
      {
        message: `Uspešno obrisano ${deleteResult.deletedCount} prošlih termina`,
        deletedCount: deleteResult.deletedCount,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Greška pri brisanju prošlih termina:", error);
    return NextResponse.json(
      { error: "Greška pri brisanju prošlih termina" },
      { status: 500 }
    );
  }
}
