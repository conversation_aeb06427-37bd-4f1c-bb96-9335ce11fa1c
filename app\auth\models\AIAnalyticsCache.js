import mongoose from "mongoose";

const AIAnalyticsCacheSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  analysisType: {
    type: String,
    required: true,
    enum: ["competitive", "occupancy", "revenue", "cancellation"],
  },
  dateRange: {
    type: String,
    required: true,
    enum: ["7days", "30days"],
  },
  analysis: {
    type: mongoose.Schema.Types.Mixed, // Podržava i String i Object
    required: true,
  },
  stats: {
    totalBookings: Number,
    confirmedBookings: Number,
    cancelledBookings: Number,
    cancellationRate: String,
  },
  dataHash: {
    type: String,
    required: true, // hash podataka za proveru da li su se promenili
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 sata
  },
});

// Index za brže pretraživanje i automatsko brisanje
AIAnalyticsCacheSchema.index({ userId: 1, analysisType: 1, dateRange: 1 });
AIAnalyticsCacheSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

export default mongoose.models.AIAnalyticsCache ||
  mongoose.model("AIAnalyticsCache", AIAnalyticsCacheSchema);
