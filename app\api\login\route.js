// app/api/login/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { loginRateLimit } from "../../middleware/redisRateLimit";

export async function POST(request) {
  // Rate limiting check
  const ip =
    request.ip ||
    request.headers.get("x-forwarded-for")?.split(",")[0] ||
    request.headers.get("x-real-ip") ||
    "unknown";

  const body = await request.json();
  const { email, password } = body;

  // Apply rate limiting
  try {
    const rateLimitResult = await loginRateLimit(ip, email);

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: "Previše pokušaja prijave",
          message: `<PERSON><PERSON><PERSON><PERSON> ste maksimalan broj pokušaja prijave. Pokušajte ponovo za ${rateLimitResult.retryAfter} sekundi.`,
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": "5",
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": new Date(
              rateLimitResult.resetTime
            ).toISOString(),
            "Retry-After": rateLimitResult.retryAfter.toString(),
          },
        }
      );
    }
  } catch (rateLimitError) {
    console.error("Rate limit error:", rateLimitError);
    // Nastavi sa normalnim procesom ako rate limiting ne radi
  }

  await dbConnect();

  if (!email || !password) {
    return NextResponse.json(
      { error: "Unesite email i lozinku." },
      { status: 400 }
    );
  }

  try {
    // POTRAŽIMO USERA PO EMAIL-U
    const user = await User.findOne({ email });

    if (!user) {
      console.log("User nije pronađen"); // DEBUG
      return NextResponse.json(
        { error: "Pogrešan email ili lozinka." },
        { status: 401 }
      );
    }

    // PROVERIMO HASHIRANU LOZINKU
    const isValid = await bcrypt.compare(password, user.password);

    if (!isValid) {
      return NextResponse.json(
        { error: "Pogrešan email ili lozinka." },
        { status: 401 }
      );
    }

    const token = jwt.sign(
      { sub: user._id.toString(), role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    // Create a response with the data
    const response = NextResponse.json(
      { role: user.role, user },
      { status: 200 }
    );

    // Set cookie in the response
    response.cookies.set({
      name: "token",
      value: token,
      httpOnly: true,
      path: "/",
      maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
    });

    return response;
  } catch (error) {
    console.error("Login API greška:", error);
    return NextResponse.json({ error: "Greška na serveru." }, { status: 500 });
  }
}
