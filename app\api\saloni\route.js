// app/api/saloni/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";

export async function GET() {
  await dbConnect();

  const saloni = await Salon.find({});
  return NextResponse.json(saloni);
}

export async function POST(request) {
  await dbConnect();

  const body = await request.json();
  const { id, ime, radnoVreme, email, telefon, role, bookingLink } = body;

  try {
    const noviSalon = await Salon.create({
      id,
      ime,
      radnoVreme,
      email,
      telefon,
      role,
      bookingLink,
    });

    return NextResponse.json(noviSalon, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 400 });
  }
}
