// app/api/me/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../auth/lib/mongoose";
import User from "../../auth/models/User";
export async function GET(request) {
  // Get the URL object to extract search params
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");

  if (!userId) {
    return NextResponse.json(
      { error: "Nedostaje ID korisnika kao query parametar" },
      { status: 400 }
    );
  }

  try {
    await dbConnect();

    // Pronađi korisnika u bazi podataka
    const user = await User.findById(userId).lean();

    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Vrati podatke o korisniku bez osetljivih informacija
    const { password, ...userWithoutPassword } = user;

    // Vraćamo strukturu koja je kompatibilna sa postojećom
    return NextResponse.json({
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
