import { NextResponse } from "next/server";
import OpenAI from "openai";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";
import Booking from "../../../auth/models/Booking";
import PastBooking from "../../../auth/models/PastBooking";
import AIAnalyticsCache from "../../../auth/models/AIAnalyticsCache";
import { trackAIUsage, createDataHash } from "../../../utils/aiTokens";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// AI Analytics API - Optimizovano za smanjenu potrošnju tokena
// - max_tokens smanjeno sa 800 na 400
// - Cache se automatski osvežava svakog ponedeljka u 09:00
// - Korisnici ne mogu ručno da osvežavaju analitiku
export async function POST(request) {
  try {
    const {
      userId,
      analysisType = "competitive",
      dateRange = "30days",
    } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "userId je obavezan" },
        { status: 400 }
      );
    }

    // Povezivanje sa bazom
    await dbConnect();

    // Dohvatanje korisnika
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "Korisnik nije pronađen" },
        { status: 404 }
      );
    }

    // Provera plana - AI analitika samo za Standard Plus i više
    const userPlan = user.plan || "standard";
    if (userPlan === "standard") {
      return NextResponse.json(
        { error: "AI analitika je dostupna samo za Standard Plus plan i više" },
        { status: 403 }
      );
    }

    // Računanje datuma na osnovu dateRange
    const daysBack = dateRange === "7days" ? 7 : 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysBack);

    // Dohvatanje zakazivanja za analizu iz obe kolekcije (filtrirano po datumu termina)
    const [currentBookings, pastBookings] = await Promise.all([
      Booking.find({
        userId,
        date: { $gte: startDate },
      }),
      PastBooking.find({
        userId,
        date: { $gte: startDate },
      }),
    ]);

    // Kombinovanje podataka iz obe kolekcije
    const allBookings = [...currentBookings, ...pastBookings];

    // Priprema podataka za analizu
    const bookingData = allBookings.map((booking) => ({
      date: booking.date,
      time: booking.time,
      service: booking.serviceName || "Nepoznata usluga",
      status: booking.status,
      clientName: booking.clientName,
    }));

    // Kreiranje hash-a podataka za cache proveru
    const dataHash = createDataHash({
      bookingData,
      analysisType,
      dateRange,
      userServices: user.services,
    });

    // Provera cache-a (automatski se osvežava svakog ponedeljka)
    const cachedAnalysis = await AIAnalyticsCache.findOne({
      userId,
      analysisType,
      dateRange,
      expiresAt: { $gt: new Date() },
    });

    if (cachedAnalysis) {
      return NextResponse.json({
        analysis: cachedAnalysis.analysis,
        stats: cachedAnalysis.stats,
        analysisType,
        dateRange,
        cached: true,
        success: true,
        lastUpdated: cachedAnalysis.createdAt,
      });
    }

    // Kreiranje različitih tipova analiza
    let systemPrompt = "";

    switch (analysisType) {
      case "occupancy": {
        const periodLabel =
          dateRange === "7days" ? "poslednja 7 dana" : "poslednjih 30 dana";
        systemPrompt = `
Analiziraj obrasce zauzetosti salona koristeći realne podatke o zakazivanjima za period od ${periodLabel}. 
PODACI:
- Booking data (JSON array):\n${JSON.stringify(bookingData, null, 2)}
- Radno vreme salona (JSON):\n${JSON.stringify(
          user.workingHours || {},
          null,
          2
        )}
- Kontekst: Salon koristi ZakaziAI booking sistem sa AI funkcionalnostima.
PERIOD: ${
          dateRange === "7days"
            ? "Kratkoročna analiza (7 dana)"
            : "Dugoročna analiza (30 dana)"
        }

ZADACI:
1. Izračunaj metrike tačno iz bookingData za period ${periodLabel}.
2. Identifikuj najzauzetije sate i dane.
3. Primenom radnog vremena proceni iskorišćenost kapaciteta.
4. Kreiraj JSON odgovor tačno u sledećem formatu (sa stvarnim vrednostima):
{
  "summaryText": "Analiza zauzetosti za ${periodLabel} pokazuje ...",
  "metrics": {
    "occupancyRate": NUMBER,            // decimal izračunat kao (broj iskorišćenih slotova) / (ukupni raspoloživi slotovi) za period
    "peakHours": ["HH:mm-HH:mm", ...],  // najzauzetiji satni intervali
    "busiestDay": "ponedeljak|utorak|...", // dan sa najvećom iskorišćenošću
    "averageBookingsPerDay": NUMBER      // prosečan broj booking-a po danu
  },
  "trendData": [
    /* Ako je 7days: po danu u nedelji, ako 30days: po nedelji */
    ${
      dateRange === "7days"
        ? `{
      "period": "Ponedeljak", 
      "occupancy": NUMBER, 
      "bookings": NUMBER
    },
    {
      "period": "Utorak", 
      "occupancy": NUMBER, 
      "bookings": NUMBER
    },
    /* ... Ostali dani */`
        : `{
      "period": "Nedelja 1", 
      "occupancy": NUMBER, 
      "bookings": NUMBER
    },
    {
      "period": "Nedelja 2", 
      "occupancy": NUMBER, 
      "bookings": NUMBER
    },
    /* ... Nedelje u mesecu */`
    }
  ],
  "recommendations": [
    { "text": "Konkretna preporuka za optimizaciju radnog vremena ili povećanje popunjenosti: ..." },
    { "text": "Druga preporuka, npr. raspoređivanje dodatnih termina u najtraženijim periodima." }
  ]
}

VAŽNO:
- Koristi samo bookingData iz perioda ${periodLabel}, izračunaj stvarne brojeve.
- Fokusiraj se na ${
          dateRange === "7days"
            ? "kratkoročne trendove i brze optimizacije"
            : "dugoročne obrasce i strateške preporuke"
        }.
- Objasni u summaryText ključne nalaze, a u recommendations daj konkretne akcione korake (npr. produženje radnog vremena, dodavanje termina u određene sate, pomeranje pauza).
`;
        break;
      }

      case "revenue": {
        const periodLabel =
          dateRange === "7days" ? "poslednja 7 dana" : "poslednjih 30 dana";
        systemPrompt = `
Analiziraj prihode salona za period od ${periodLabel} koristeći bookingData i cene usluga. 
PODACI:
- Booking data (JSON array):\n${JSON.stringify(bookingData, null, 2)}
- Usluge i cene (naziv, cena, trajanje):\n${
          user.services
            ?.map((s) => `${s.name}: ${s.price} RSD (${s.durationMins}min)`)
            .join("\n") || "Nema definisanih usluga"
        }
- Kontekst: Salon koristi ZakaziAI booking sistem.
PERIOD: ${
          dateRange === "7days"
            ? "Kratkoročna finansijska analiza (7 dana)"
            : "Dugoročna finansijska analiza (30 dana)"
        }

ZADACI:
1. Izračunaj:
   - totalRevenue: ukupan prihod za ${periodLabel}.
   - averageRevenuePerBooking: totalRevenue / broj realizovanih booking-a.
   - revenueChangePercent: uporedi sa prethodnim periodom iste dužine (ako imaš podatke; ako nema, vrati 0 ili napiši “nedovoljno podataka”).
   - topService: usluga sa najvećim prihodom ili brojem booking-a.
2. Kreiraj trendData:
   ${
     dateRange === "7days"
       ? `[
    { "period": "Dan 1", "revenue": NUMBER, "bookings": NUMBER },
    ... do Dan 7`
       : `[
    { "period": "Nedelja 1", "revenue": NUMBER, "bookings": NUMBER },
    ... do Nedelja 4`
   }
   ]
3. Formiraj recommendations sa konkretnim smernicama za povećanje prihoda:
   - npr. “Promoviši X uslugu u slabijim danima”, “Poviši cenu za uslugu Y u periodima visoke potražnje”, “Ponudi paket usluga za povećanje vrednosti po klijentu”.

KONAČNI JSON:
{
  "summaryText": "Analiza prihoda za ${periodLabel} pokazuje ...",
  "metrics": {
    "totalRevenue": NUMBER,
    "averageRevenuePerBooking": NUMBER,
    "revenueChangePercent": NUMBER_OR_STRING,
    "topService": "Naziv usluge"
  },
  "trendData": [ /* kao gore */ ],
  "recommendations": [
    { "text": "Konkretna preporuka..." },
    { "text": "Druga preporuka..." }
  ]
}

VAŽNO:
- Koristi stvarne cene i bookingData.
- Fokus: ${
          dateRange === "7days"
            ? "dnevne varijacije i brze prilike"
            : "nedeljne trendove i dugoročnu strategiju"
        }.
- Ne izmišljaj brojeve.
`;
        break;
      }

      case "cancellation": {
        const periodLabel =
          dateRange === "7days" ? "poslednja 7 dana" : "poslednjih 30 dana";
        systemPrompt = `
Analiziraj otkazivanja za period od ${periodLabel}. 
PODACI:
- Booking data (JSON array) sa statusima (cancelled, confirmed, completed):\n${JSON.stringify(
          bookingData,
          null,
          2
        )}
- Kontekst: Salon koristi ZakaziAI booking sistem.
PERIOD: ${
          dateRange === "7days"
            ? "Kratkoročna analiza otkazivanja (7 dana)"
            : "Dugoročna analiza otkazivanja (30 dana)"
        }

ZADACI:
1. Izračunaj:
   - cancellationRate: broj otkazanih / ukupnih booking-a za period.
   - mostCancelledService: usluga sa najviše otkazivanja ili “N/A” ako nema.
   - worstDay: dan sa najviše otkazivanja.
   - averageCancellationTime: prosečno vreme između rezervacije i otkazivanja, ako podaci omogućavaju; inače “N/A”.
2. Kreiraj trendData:
   ${
     dateRange === "7days"
       ? `[
    { "period": "Ponedeljak", "cancellations": NUMBER, "total": NUMBER },
    ... do Nedelja`
       : `[
    { "period": "Nedelja 1", "cancellations": NUMBER, "total": NUMBER },
    ... do Nedelja 4`
   }
   ]
3. Formiraj recommendations:
   - Za ${
     dateRange === "7days" ? "hitne mere" : "strateške mere"
   }: npr. “Pojačaj podsetnike za klijente sa višim rizikom otkazivanja”, “Ponuži fleksibilnije uslove za smenu termina”, “Analiziraj razloge otkazivanja kroz anketu”.

KONAČNI JSON:
{
  "summaryText": "Analiza otkazivanja za ${periodLabel} pokazuje ...",
  "metrics": {
    "cancellationRate": NUMBER,
    "mostCancelledService": "Naziv usluge ili N/A",
    "worstDay": "dan",
    "averageCancellationTime": "HH:mm ili N/A"
  },
  "trendData": [ /* kao gore */ ],
  "recommendations": [
    { "text": "Konkretna ${
      dateRange === "7days" ? "hitna" : "strateška"
    } preporuka..." },
    { "text": "Još jedna preporuka..." }
  ]
}

VAŽNO:
- Koristi stvarne statuse iz bookingData.
- Fokus: ${
          dateRange === "7days"
            ? "hitne mere za smanjenje otkazivanja"
            : "dugoročne strategije prevencije"
        }.
- Ne generiši izmišljene vrednosti.
`;
        break;
      }

      case "competitive": {
        const periodLabel =
          dateRange === "7days" ? "poslednja 7 dana" : "poslednjih 30 dana";
        systemPrompt = `
Kreiraj konkurentsku analizu salona na osnovu podataka:
- Salon info: naziv ${user.name}, plan ${userPlan}, usluge i cene:\n${
          user.services
            ?.map((s) => `${s.name}: ${s.price}RSD (${s.durationMins}min)`)
            .join("; ") || "Nema usluga"
        }
- Radno vreme:\n${JSON.stringify(user.workingHours || {}, null, 2)}
- BookingData za ${periodLabel}:\n${JSON.stringify(bookingData, null, 2)}
Kontekst: Salon već koristi ZakaziAI booking sistem; ne preporučuj menjanje sistema.

ZADACI:
1. Izračunaj:
   - marketPosition: proceni poziciju na tržištu (Premium/Srednji/Ekonomski) na osnovu cena i učestalosti rezervacija.
   - priceCompetitiveness: brojčano 0–1 oceni konkurentnost cena.
   - serviceQuality: 0–1 oceni kvalitet (npr. na osnovu popunjenosti i stope otkazivanja).
   - competitiveAdvantage: identifikuj jaku stranu (npr. tehnologija, fleksibilnost, personalizacija).
2. Kreiraj trendData za uporedne dimenzije:
   [
     { "period": "Cene", "yourSalon": AVERAGE_PRICE, "competition": ESTIMATE },
     { "period": "Kvalitet", "yourSalon": SCORE, "competition": ESTIMATE },
     { "period": "Dostupnost", "yourSalon": SCORE, "competition": ESTIMATE },
     /* po potrebi ostalo kao Tehnologija */
   ]
3. Formiraj recommendations:
   - ${
     dateRange === "7days" ? "Kratkoročne prilike" : "Dugoročne strategije"
   }: npr. “Istakni premium usluge u marketingu”, “Prilagodi cene u slabijim periodima”, “Unapredi online vidljivost” itd.

KONAČNI JSON:
{
  "summaryText": "Konkurentska analiza za ${periodLabel} pokazuje ...",
  "metrics": {
    "marketPosition": "Premium|Srednji|Ekonomski",
    "priceCompetitiveness": NUMBER,
    "serviceQuality": NUMBER,
    "competitiveAdvantage": "Opis prednosti"
  },
  "trendData": [ /* kao gore */ ],
  "recommendations": [
    { "text": "Konkretna ${
      dateRange === "7days" ? "kratkoročna" : "strateška"
    } preporuka..." },
    { "text": "Još jedna preporuka..." }
  ]
}

VAŽNO:
- Koristi stvarne cene, radno vreme i bookingData.
- Fokus: ${
          dateRange === "7days"
            ? "hitne konkurentske prilike"
            : "dugoročnu poziciju i diferencijaciju"
        }.
- Ne izmišljaj brojeve ili neosnovane procene.
`;
        break;
      }
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4.1-nano",
      messages: [
        { role: "system", content: systemPrompt },
        {
          role: "user",
          content:
            "Molim te analiziraj podatke i daj preporuke u JSON formatu.",
        },
      ],
      max_tokens: 800,
      temperature: 0.3,
    });

    let analysis;
    try {
      // Pokušaj da parsiraš JSON odgovor
      const rawContent = response.choices[0].message.content;
      analysis = JSON.parse(rawContent);
    } catch (parseError) {
      console.error("JSON parse error:", parseError);
      // Fallback na običan tekst ako JSON parsing ne uspe
      analysis = {
        summaryText: response.choices[0].message.content,
        metrics: {},
        trendData: [],
        recommendations: [],
      };
    }

    // Kreiranje osnovnih statistika (koristimo allBookings umesto bookings)
    const stats = {
      totalBookings: allBookings.length,
      confirmedBookings: allBookings.filter(
        (b) => b.status === "confirmed" || b.status === "completed"
      ).length,
      cancelledBookings: allBookings.filter((b) => b.status === "cancelled")
        .length,
      completedBookings: allBookings.filter(
        (b) => b.status === "confirmed" || b.status === "completed"
      ).length, // Isti kao confirmedBookings za backward compatibility
      cancellationRate:
        allBookings.length > 0
          ? (
              (allBookings.filter((b) => b.status === "cancelled").length /
                allBookings.length) *
              100
            ).toFixed(1)
          : 0,
    };

    // Tracking AI usage
    await trackAIUsage(userId, "analytics", response.usage, "gpt-4.1-nano", {
      analysisType,
      dateRange,
      bookingCount: allBookings.length,
    });

    // Čuvanje u cache
    try {
      // Obriši stari cache za isti tip analize
      await AIAnalyticsCache.deleteMany({
        userId,
        analysisType,
        dateRange,
      });

      // Sačuvaj novi cache
      const cacheEntry = new AIAnalyticsCache({
        userId,
        analysisType,
        dateRange,
        analysis,
        stats,
        dataHash,
      });
      await cacheEntry.save();
    } catch (cacheError) {
      console.error("Cache save error:", cacheError);
      // Ne prekidaj izvršavanje ako cache ne radi
    }

    return NextResponse.json({
      analysis,
      stats,
      analysisType,
      dateRange,
      cached: false,
      success: true,
      lastUpdated: new Date(),
    });
  } catch (error) {
    console.error("AI Analytics Error:", error);
    return NextResponse.json(
      { error: "Greška pri AI analizi" },
      { status: 500 }
    );
  }
}
