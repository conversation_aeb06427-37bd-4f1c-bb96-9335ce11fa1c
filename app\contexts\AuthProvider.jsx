"use client";

import { createContext, useContext, useState, useEffect } from "react";

// Kreiramo Context
const AuthContext = createContext({
  user: null,
  loading: true,
  login: async () => {},
  logout: () => {},
  refreshUserData: async () => {},
  updateUserData: () => {},
});

// Provider koji celoj aplikaciji daje pristup auth podacima
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Učitaj korisnika iz localStorage pri inicijalnom učitavanju
  useEffect(() => {
    const loadUserFromStorage = () => {
      try {
        const storedUser = localStorage.getItem("user");
        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }
      } catch (error) {
        console.error(
          "Greška pri učitavanju korisnika iz localStorage:",
          error
        );
      } finally {
        setLoading(false);
      }
    };

    // <PERSON>risti setTimeout da se izbegne SSR greške
    if (typeof window !== "undefined") {
      loadUserFromStorage();
    } else {
      setLoading(false);
    }
  }, []);

  // Funkcija koja se poziva iz LoginFoma: šalje POST /api/login i upisuje user u state
  async function login(email, password) {
    setLoading(true);
    const res = await fetch("/api/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email: email.toLowerCase().trim(),
        password: password.trim(),
      }),
    });
    if (!res.ok) {
      const err = await res.json();
      setLoading(false);
      throw new Error(err.error || "Greška pri logovanju");
    }
    const data = await res.json(); // { email, role }
    setUser(data);
    // Sačuvaj korisnika u localStorage
    localStorage.setItem("user", JSON.stringify(data));
    setLoading(false);
    return data;
  }

  async function register(
    name,
    email,
    password,
    phone,
    businessName,
    businessType,
    plan = "standard"
  ) {
    setLoading(true);
    const res = await fetch("/api/register", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        name,
        email: email.toLowerCase().trim(),
        password: password.trim(),
        phone,
        businessName,
        businessType,
        plan,
      }),
    });
    if (!res.ok) {
      const err = await res.json();
      setLoading(false);
      throw new Error(err.error || "Registracija nije uspela");
    }
    // ne logujemo odmah, samo završimo loading
    setLoading(false);
  }

  // Funkcija za osvežavanje korisničkih podataka
  const refreshUserData = async () => {
    try {
      if (!user || (!user._id && !user.user?._id)) {
        console.warn("Ne mogu osvežiti podatke jer korisnik nije definisan");
        return;
      }

      setLoading(true);
      const userId = user.user?._id || user._id;
      const res = await fetch(`/api/me?userId=${userId}`);

      if (!res.ok) {
        throw new Error("Nije moguće osvežiti korisničke podatke");
      }

      const userData = await res.json();
      setUser(userData);
      // Ažuriraj korisnika u localStorage
      localStorage.setItem("user", JSON.stringify(userData));
    } catch (error) {
      console.error("Greška pri osvežavanju korisničkih podataka:", error);
    } finally {
      setLoading(false);
    }
  };

  // Funkcija za direktno ažuriranje korisničkih podataka u kontekstu
  const updateUserData = (newUserData) => {
    setUser(newUserData);
    // Ažuriraj korisnika u localStorage
    localStorage.setItem("user", JSON.stringify(newUserData));
  };

  // Logout – briše user iz state i localStorage
  const logout = async () => {
    setUser(null);
    localStorage.removeItem("user");
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        setLoading,
        login,
        logout,
        register,
        refreshUserData,
        updateUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook da bi komponente lako mogle da dođu do auth state-a
export function useAuth() {
  return useContext(AuthContext);
}
