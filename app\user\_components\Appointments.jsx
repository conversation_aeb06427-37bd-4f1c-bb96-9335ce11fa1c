"use client";

import React, { useState, useEffect } from "react";
import {
  format,
  isToday as isDateToday,
  isTomorrow as isDateTomorrow,
  parseISO,
  compareAsc,
} from "date-fns";
import { sr } from "date-fns/locale";
import BookingForm from "./BookingForm";
import { useAuth } from "@/app/contexts/AuthProvider";
import HybridBookingForm from "./HybridBookingForm";

export default function Appointments({ user }) {
  const { refreshUserData } = useAuth();
  const [showForm, setShowForm] = useState(false);
  const [useAIBooking, setUseAIBooking] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Pomoćne funkcije za formatiranje - koristimo date-fns
  const parseDateSafely = (dateStr) => {
    try {
      // Prvo pokušamo parseISO koji je precizniji za ISO formate
      const parsed = parseISO(dateStr);
      // Provera da li je validan datum
      if (isNaN(parsed.getTime())) {
        // Fallback na standardni Date konstruktor
        return new Date(dateStr);
      }
      return parsed;
    } catch (e) {
      console.warn("Problem parsing date:", dateStr, e);
      return new Date(); // Fallback na današnji datum
    }
  };

  // Formatiranje vremena
  const formatTime = (timeStr) => {
    if (!timeStr) return "";
    if (typeof timeStr === "string") {
      const [hours, minutes] = timeStr.split(":");
      if (!hours || !minutes) return timeStr;
      return `${hours.padStart(2, "0")}:${minutes.padStart(2, "0")}`;
    }
    return timeStr;
  };

  // Formatiranje datuma i vremena zajedno
  const formatDateTime = (dateStr, timeStr) => {
    try {
      const date = parseDateSafely(dateStr);
      const dayName = formatDayName(dateStr);
      const formattedDate = format(date, "d. MMMM yyyy.", { locale: sr });

      // Handle the case when timeStr is not provided
      if (timeStr) {
        const formattedTime = formatTime(timeStr);
        return `${dayName} ${formattedDate} - ${formattedTime}`;
      } else {
        return `${dayName} ${formattedDate}`;
      }
    } catch (e) {
      console.error("Error formatting date time:", e);
      return timeStr ? `${dateStr} ${timeStr}` : dateStr;
    }
  };

  // Provera da li je datum danas
  const isToday = (dateStr) => {
    if (!dateStr) return false;
    try {
      const date = parseDateSafely(dateStr);
      return isDateToday(date);
    } catch (e) {
      console.warn("Error checking isToday:", e);
      return false;
    }
  };

  // Provera da li je datum sutra
  const isTomorrow = (dateStr) => {
    if (!dateStr) return false;
    try {
      const date = parseDateSafely(dateStr);
      return isDateTomorrow(date);
    } catch (e) {
      console.warn("Error checking isTomorrow:", e);
      return false;
    }
  };

  // Formatiranje imena dana
  const formatDayName = (dateStr) => {
    if (!dateStr) return "";
    try {
      const date = parseDateSafely(dateStr);
      const dayNames = [
        "Nedelja",
        "Ponedeljak",
        "Utorak",
        "Sreda",
        "Četvrtak",
        "Petak",
        "Subota",
      ];
      return dayNames[date.getDay()];
    } catch (e) {
      console.warn("Error formatting day name:", e);
      return "";
    }
  };

  // Prevođenje statusa na srpski
  const translateStatus = (status) => {
    const statusMap = {
      pending: "Na čekanju",
      confirmed: "Potvrđeno",
      cancelled: "Otkazano",
    };
    return statusMap[status] || status;
  };

  // Boje za različite statuse
  const statusColors = {
    pending: "bg-yellow-100 text-yellow-800",
    confirmed: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800",
    completed: "bg-blue-100 text-blue-800",
  };

  // Funkcija za grupisanje zakazivanja po datumima
  const groupBookingsByDate = (bookingsList) => {
    const grouped = {};

    // Prvo sortirajmo zakazivanja po vremenu
    const sortedBookings = [...bookingsList].sort((a, b) => {
      if (!a.date || !b.date) return 0;

      // Prvo poredimo datume
      const dateA = parseDateSafely(a.date);
      const dateB = parseDateSafely(b.date);
      const dateDiff = compareAsc(dateA, dateB);

      if (dateDiff !== 0) return dateDiff;

      // Ako su datumi isti, poredimo vremena
      if (a.time && b.time) {
        const [hoursA, minutesA] = a.time.split(":").map(Number);
        const [hoursB, minutesB] = b.time.split(":").map(Number);

        if (hoursA !== hoursB) return hoursA - hoursB;
        return minutesA - minutesB;
      }

      return 0;
    });

    sortedBookings.forEach((booking) => {
      if (!booking.date) return;

      // Formatiramo datum kao ključ (samo datum bez vremena)
      const dateStr = booking.date.split("T")[0]; // Uzima samo YYYY-MM-DD deo

      // Ako datum već postoji u mapi, dodajemo zakazivanje
      if (grouped[dateStr]) {
        grouped[dateStr].push(booking);
      } else {
        // Inače, kreiramo novi niz sa prvim zakazivanjem
        grouped[dateStr] = [booking];
      }
    });

    return grouped;
  };

  // Funkcija za učitavanje usluga
  const loadServices = async () => {
    try {
      const userId = user?.user?._id || user?._id;
      if (!userId) return;

      const response = await fetch(`/api/services/get-all?userId=${userId}`);

      if (!response.ok) {
        throw new Error("Greška pri učitavanju usluga");
      }

      const data = await response.json();
      setServices(data.services || []);
    } catch (err) {
      console.error("Greška pri učitavanju usluga:", err);
      // Kao fallback koristimo usluge iz user objekta
      if (user && (user.services || user.user?.services)) {
        setServices(user.user?.services || user.services || []);
      }
    }
  };

  // Učitavanje zakazivanja
  const loadBookings = async () => {
    setLoading(true);
    setError("");

    const userId = user?.user?._id || user?._id;

    if (!userId) {
      setError("Nije moguće učitati zakazivanja - korisnik nije pronađen");
      setLoading(false);
      return;
    }

    try {
      // Uvek učitaj zakazivanja direktno sa API-ja iz Booking kolekcije

      const response = await fetch(`/api/bookings?userId=${userId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Greška pri učitavanju zakazivanja");
      }

      const data = await response.json();

      if (!data.bookings || !Array.isArray(data.bookings)) {
        setBookings([]);
        return;
      }

      // Filtriramo da uklonimo nevažeće vrednosti
      const validBookings = data.bookings.filter((booking) => {
        if (!booking || typeof booking !== "object") {
          return false;
        }

        if (!booking.clientName) {
          return false;
        }

        return true;
      });

      // Sortiramo zakazivanja po datumu, rastući redosled (prvo najbliži datumi)
      const sortedBookings = [...validBookings].sort((a, b) => {
        // Ako nemaju datum, stavi ih na kraj
        if (!a.date) return 1;
        if (!b.date) return -1;

        // Pokušaj kreirati Date objekte iz datuma zakazivanja
        const dateA = parseDateSafely(a.date);
        const dateB = parseDateSafely(b.date);

        // Koristi date-fns compareAsc za preciznije poređenje datuma
        return compareAsc(dateA, dateB);
      });

      setBookings(sortedBookings);
    } catch (err) {
      console.error("Greška pri učitavanju zakazivanja:", err);
      setError("Došlo je do greške pri učitavanju zakazivanja: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Učitaj zakazivanja i usluge kada se komponenta učita
  useEffect(() => {
    loadBookings();
    loadServices();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Ponovo učitaj usluge kada se pokaže forma za zakazivanje
  useEffect(() => {
    if (showForm) {
      loadServices();
    }
  }, [showForm]);

  // Promeni status zakazivanja
  const handleStatusChange = async (bookingId, newStatus) => {
    if (!bookingId || !newStatus) return;

    setLoading(true);

    try {
      const userId = user?.user?._id || user?._id;

      const response = await fetch("/api/bookings/update-status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          bookingId,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Greška pri ažuriranju statusa");
      }

      // Osvežite listu zakazivanja nakon promene statusa
      await loadBookings();
    } catch (err) {
      console.error("Greška pri promeni statusa:", err);
      setError("Došlo je do greške pri promeni statusa");
    } finally {
      setLoading(false);
    }
  };

  // Završetak dodavanja novog zakazivanja
  const handleBookingComplete = async (success) => {
    if (success) {
      setShowForm(false);
      await Promise.all([loadBookings(), loadServices(), refreshUserData()]);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Zakazivanja
      </h2>
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        <p className="text-[var(--foreground)]/70 mb-4">
          Ovde možete videti sva zakazivanja i upravljati njima.
        </p>

        <div className="flex flex-wrap justify-between items-center gap-4 mb-10">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => {
                setShowForm(!showForm);
                setUseAIBooking(false);
              }}
              className={`px-4 py-2 rounded-md transition-colors ${
                showForm && !useAIBooking
                  ? "bg-gray-500 text-white"
                  : "bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white"
              }`}
            >
              {showForm && !useAIBooking ? "Otkaži" : "Klasično zakazivanje"}
            </button>

            <button
              onClick={() => {
                setShowForm(!showForm || !useAIBooking);
                setUseAIBooking(!useAIBooking);
              }}
              className={`px-4 py-2 rounded-md transition-colors ${
                showForm && useAIBooking
                  ? "bg-gray-500 text-white"
                  : "bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
              }`}
            >
              {showForm && useAIBooking ? "Otkaži" : "🤖 AI Zakazivanje"}
            </button>
          </div>
        </div>

        {showForm && (
          <div className="mb-6 p-4 bg-[var(--background)] rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-[var(--foreground)]">
              {useAIBooking ? "🤖 AI Zakazivanje" : "Novo zakazivanje"}
            </h3>
            {useAIBooking ? (
              <HybridBookingForm
                user={user}
                services={services}
                onComplete={handleBookingComplete}
              />
            ) : (
              <BookingForm
                user={user}
                services={services}
                onComplete={handleBookingComplete}
              />
            )}
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center p-4">
            <p className="text-[var(--foreground)]/60">
              Učitavanje zakazivanja...
            </p>
          </div>
        ) : bookings.length === 0 ? (
          <div className="bg-[var(--background)] p-4 rounded-lg text-center">
            <p className="text-[var(--foreground)]/60">
              Nemate nijedno zakazivanje.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {(() => {
              // Grupisanje zakazivanja po datumima
              const groupedBookings = groupBookingsByDate(bookings);

              // Sortiranje datuma - prvo današnji, pa sutrašnji, pa ostali hronološki
              const sortedDates = Object.keys(groupedBookings).sort((a, b) => {
                // Posebno tretiramo današnji i sutrašnji dan
                const aIsToday = isToday(a);
                const bIsToday = isToday(b);
                const aIsTomorrow = isTomorrow(a);
                const bIsTomorrow = isTomorrow(b);

                // Današnji datum uvek ima prednost
                if (aIsToday && !bIsToday) return -1;
                if (!aIsToday && bIsToday) return 1;

                // Zatim sutrašnji datum
                if (aIsTomorrow && !bIsTomorrow && !bIsToday) return -1;
                if (!aIsTomorrow && bIsTomorrow && !aIsToday) return 1;

                // Za ostale datume, sortiramo hronološki
                const dateA = parseDateSafely(a);
                const dateB = parseDateSafely(b);
                return compareAsc(dateA, dateB);
              });

              // Ako nema zakazivanja nakon grupisanja
              if (sortedDates.length === 0) {
                return (
                  <div className="bg-[var(--background)] p-4 rounded-lg text-center">
                    <p className="text-[var(--foreground)]/60">
                      Nemate nijedno zakazivanje.
                    </p>
                  </div>
                );
              }

              // Renderovanje zakazivanja po datumima
              return sortedDates.map((dateStr) => {
                const bookingsForDate = groupedBookings[dateStr];
                const datumJeDanas = isToday(dateStr);
                const datumJeSutra = isTomorrow(dateStr);
                return (
                  <div
                    key={dateStr}
                    className={`bg-[var(--background)] rounded-lg overflow-hidden
                      ${
                        datumJeDanas
                          ? "ring-2 ring-blue-500 border border-blue-500 shadow-lg relative z-10 sm:scale-105 mb-4 sm:mb-8 mt-2 sm:mt-4"
                          : datumJeSutra
                          ? "ring-1 ring-green-500 border border-green-300 shadow-md mb-3 sm:mb-6"
                          : "shadow-sm mb-3 sm:mb-4"
                      }`}
                  >
                    {/* Zaglavlje sekcije sa datumom */}
                    <div
                      className={`py-3 px-3 sm:px-4 ${
                        datumJeDanas
                          ? "bg-blue-500 text-white"
                          : datumJeSutra
                          ? "bg-green-500 text-white"
                          : "bg-[var(--surface)]"
                      }`}
                    >
                      {/* Mobile Layout */}
                      <div className="flex flex-col space-y-2 sm:hidden">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <h3 className="font-semibold text-sm">
                              {formatDateTime(dateStr)}
                            </h3>
                            {datumJeDanas && (
                              <div className="relative ml-2">
                                <span className="text-xs bg-white text-blue-500 px-2 py-0.5 rounded-full font-bold border border-white shadow-sm animate-pulse">
                                  DANAS
                                </span>
                                <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full animate-ping"></span>
                              </div>
                            )}
                            {datumJeSutra && (
                              <span className="ml-2 text-xs bg-white text-green-500 px-2 py-0.5 rounded-full font-medium">
                                Sutra
                              </span>
                            )}
                          </div>
                          <span
                            className={`text-xs ${
                              datumJeDanas || datumJeSutra
                                ? "text-white/80"
                                : "text-[var(--foreground)]/60"
                            }`}
                          >
                            {bookingsForDate.length} zakazivanja
                          </span>
                        </div>
                      </div>

                      {/* Desktop Layout */}
                      <div className="hidden sm:flex justify-between items-center">
                        <div className="flex items-center">
                          <h3 className="font-semibold"></h3>
                          {datumJeDanas && (
                            <div className="relative">
                              <span className="ml-2 text-xs bg-white text-blue-500 px-3 py-1 rounded-full font-bold border-2 border-white shadow-sm animate-pulse">
                                DANAS
                              </span>
                              <span className="absolute -top-3 -right-3 h-4 w-4 bg-red-500 rounded-full animate-ping"></span>
                            </div>
                          )}
                          {datumJeSutra && (
                            <span className="ml-2 text-xs bg-white text-green-500 px-2 py-0.5 rounded-full">
                              Sutra
                            </span>
                          )}
                        </div>
                        <div className="font-medium text-[var(--foreground)]">
                          {formatDateTime(dateStr)}
                        </div>
                        <span
                          className={`text-sm ${
                            datumJeDanas || datumJeSutra
                              ? "text-white/80"
                              : "text-[var(--foreground)]/60"
                          }`}
                        >
                          {bookingsForDate.length} zakazivanja
                        </span>
                      </div>
                    </div>

                    {/* Zakazivanja za ovaj datum */}
                    <div className="divide-y divide-[var(--border)]">
                      {/* Sortiramo zakazivanja po vremenu */}
                      {bookingsForDate
                        .sort((a, b) => {
                          const timeA = a.time || "00:00";
                          const timeB = b.time || "00:00";
                          return timeA.localeCompare(timeB);
                        })
                        .map((booking) => (
                          <div
                            key={booking._id}
                            className="p-3 sm:p-4 hover:bg-[var(--surface)]/10 transition-colors"
                          >
                            {/* Mobile Layout */}
                            <div className="sm:hidden">
                              <div className="flex justify-between items-start mb-3">
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-medium text-[var(--foreground)] truncate">
                                    {booking.clientName}
                                  </h4>
                                  <p className="text-sm text-[var(--foreground)]/60 truncate">
                                    {booking.clientPhone}
                                  </p>
                                </div>
                                <div className="ml-3 flex-shrink-0">
                                  <span
                                    className={`inline-block px-2 py-1 rounded-full text-xs ${
                                      statusColors[booking.status] ||
                                      "bg-gray-100 text-gray-800"
                                    }`}
                                  >
                                    {translateStatus(booking.status)}
                                  </span>
                                </div>
                              </div>

                              <div className="mb-3">
                                <div className="text-sm text-[var(--foreground)] mb-1">
                                  {booking.serviceName || "Opšta usluga"}
                                </div>
                                <div className="text-sm text-[var(--foreground)]/70 font-medium">
                                  {formatTime(booking.time)}
                                </div>
                              </div>

                              <div>
                                <select
                                  className="w-full text-sm border border-[var(--border)] rounded px-3 py-2 bg-[var(--background)]"
                                  value=""
                                  onChange={(e) => {
                                    if (e.target.value) {
                                      handleStatusChange(
                                        booking._id,
                                        e.target.value
                                      );
                                      e.target.value = "";
                                    }
                                  }}
                                >
                                  <option value="">Promeni status</option>
                                  <option value="pending">Na čekanju</option>
                                  <option value="confirmed">Potvrdi</option>
                                  <option value="cancelled">Otkaži</option>
                                </select>
                              </div>
                            </div>

                            {/* Desktop Layout */}
                            <div className="hidden sm:block">
                              <div className="flex justify-between items-start mb-2">
                                <div>
                                  <h4 className="font-medium text-[var(--foreground)]">
                                    {booking.clientName}
                                  </h4>
                                  <p className="text-sm text-[var(--foreground)]/60">
                                    {booking.clientPhone}
                                  </p>
                                </div>
                                <div className="text-right">
                                  <span
                                    className={`inline-block px-2 py-1 rounded-full text-xs ${
                                      statusColors[booking.status] ||
                                      "bg-gray-100 text-gray-800"
                                    }`}
                                  >
                                    {translateStatus(booking.status)}
                                  </span>
                                </div>
                              </div>

                              <div className="flex justify-between items-center">
                                <div className="text-[var(--foreground)]">
                                  {booking.serviceName || "Opšta usluga"}
                                  <span className="ml-2 text-[var(--foreground)]/70 text-sm">
                                    {formatTime(booking.time)}
                                  </span>
                                </div>
                                <div>
                                  <select
                                    className="text-sm border border-[var(--border)] rounded px-2 py-1 bg-[var(--background)]"
                                    value=""
                                    onChange={(e) => {
                                      if (e.target.value) {
                                        handleStatusChange(
                                          booking._id,
                                          e.target.value
                                        );
                                        e.target.value = "";
                                      }
                                    }}
                                  >
                                    <option value="">Promeni status</option>
                                    <option value="pending">Na čekanju</option>
                                    <option value="confirmed">Potvrdi</option>
                                    <option value="cancelled">Otkaži</option>
                                  </select>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                );
              });
            })()}
          </div>
        )}
      </div>
    </div>
  );
}
