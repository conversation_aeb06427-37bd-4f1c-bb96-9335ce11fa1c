// app/api/bookings/update-status/route.js
import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";
import Booking from "../../../auth/models/Booking";
import PastBooking from "../../../auth/models/PastBooking";

export async function POST(request) {
  await dbConnect();

  // Parse the JSON body
  const body = await request.json();
  const { userId, bookingId, status } = body;

  // Validacija podataka
  if (!userId || !bookingId || !status) {
    return NextResponse.json(
      { error: "Nedostaju potrebni podaci" },
      { status: 400 }
    );
  }

  // Validacija statusa - mapiramo "completed" na "confirmed" za konzistentnost
  const validStatuses = ["pending", "confirmed", "cancelled"];
  let finalStatus = status;

  // Mapiranje "completed" na "confirmed" za backward compatibility
  if (status === "completed") {
    finalStatus = "confirmed";
  }

  if (!validStatuses.includes(finalStatus)) {
    return NextResponse.json({ error: "Nevažeći status" }, { status: 400 });
  }

  try {
    // Pronađi zakazivanje u Booking kolekciji
    const booking = await Booking.findOne({
      _id: bookingId,
      userId: userId,
    });

    if (!booking) {
      return NextResponse.json(
        { error: "Rezervacija nije pronađena" },
        { status: 404 }
      );
    }

    // Ako je status confirmed ili cancelled, prebaci u pastBookings
    if (finalStatus === "confirmed" || finalStatus === "cancelled") {
      // Kreiraj novi PastBooking zapis
      const pastBooking = new PastBooking({
        userId: booking.userId,
        clientName: booking.clientName,
        clientEmail: booking.clientEmail,
        clientPhone: booking.clientPhone,
        serviceId: booking.serviceId,
        serviceName: booking.serviceName,
        date: booking.date,
        time: booking.time,
        status: finalStatus,
        originalBookingId: booking._id,
        movedToPastAt: new Date(),
      });

      // Sačuvaj u pastBookings kolekciju
      await pastBooking.save();

      // Obriši iz glavne bookings kolekcije
      await Booking.findByIdAndDelete(bookingId);

      return NextResponse.json({
        success: true,
        message: `Rezervacija je uspešno ${
          finalStatus === "confirmed" ? "potvrđena" : "otkazana"
        } i prebačena u istoriju`,
        movedToPast: true,
      });
    } else {
      // Za pending status, samo ažuriraj postojeći booking
      booking.status = finalStatus;
      await booking.save();

      return NextResponse.json({
        success: true,
        message: "Status rezervacije je uspešno ažuriran",
        movedToPast: false,
      });
    }
  } catch (error) {
    console.error("Error updating booking status:", error);
    return NextResponse.json({ error: "Greška na serveru" }, { status: 500 });
  }
}
