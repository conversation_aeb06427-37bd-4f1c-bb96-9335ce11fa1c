"use client";

import { useState } from "react";
import FreeTrialForm from "./FreeTrialForm";

function CTA() {
  const [showFreeTrialForm, setShowFreeTrialForm] = useState(false);

  const handleFreeTrialClick = (e) => {
    e.preventDefault();
    setShowFreeTrialForm(true);
  };

  const handleLearnMoreClick = (e) => {
    e.preventDefault();
    // Scroll to features section
    document.getElementById("features")?.scrollIntoView({
      behavior: "smooth",
    });
  };
  return (
    <section
      id="contact"
      className="py-16 sm:py-24 bg-gradient-to-r from-teal-500 to-blue-600 relative overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-teal-400/20 to-blue-500/20 animate-gradient"></div>

        {/* Booking-related decorative elements */}
        <div className="absolute top-10 left-10 w-32 h-32 opacity-40 animate-float">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-white"
          >
            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
          </svg>
        </div>

        <div className="absolute bottom-10 right-10 w-24 h-24 opacity-40 animate-float animation-delay-2000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-white"
          >
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
          </svg>
        </div>

        <div className="absolute top-1/2 left-1/4 w-16 h-16 opacity-40 animate-float animation-delay-4000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-white"
          >
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z" />
          </svg>
        </div>

        <div className="absolute bottom-1/4 left-3/4 w-20 h-20 opacity-40 animate-float animation-delay-1000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-white"
          >
            <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
          </svg>
        </div>

        <div className="absolute top-20 right-1/3 w-18 h-18 opacity-40 animate-float animation-delay-3000">
          <svg
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-full h-full text-white"
          >
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
          </svg>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl animate-fade-in-up">
            Spremni da transformišete Vaše iskustvo zakazivanja?
          </h2>
          <p className="mt-4 text-xl text-white/80 max-w-2xl mx-auto animate-fade-in-up animation-delay-200">
            Pridružite se preduzećima koja su pojednostavila zakazivanje termina
            uz Zakaži AI.
          </p>
          <div className="mt-8 flex justify-center animate-fade-in-up animation-delay-400">
            <div className="inline-flex rounded-md shadow-2xl">
              <button
                onClick={handleFreeTrialClick}
                className="group inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-md text-teal-600 bg-white hover:bg-gray-50 transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 hover:shadow-2xl relative overflow-hidden"
              >
                <span className="relative z-10">Započnite besplatno</span>
                <div className="absolute inset-0 bg-gradient-to-r from-gray-50 to-gray-100 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                <svg
                  className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300 relative z-10"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </svg>
              </button>
            </div>
            <div className="ml-3 inline-flex">
              <button
                onClick={handleLearnMoreClick}
                className="group inline-flex items-center justify-center px-8 py-4 border border-white/30 text-base font-medium rounded-md text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 hover:shadow-2xl relative overflow-hidden"
              >
                <span className="relative z-10">Saznajte više</span>
                <div className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                <svg
                  className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300 relative z-10"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {showFreeTrialForm && (
        <FreeTrialForm onClose={() => setShowFreeTrialForm(false)} />
      )}
    </section>
  );
}

export default CTA;
