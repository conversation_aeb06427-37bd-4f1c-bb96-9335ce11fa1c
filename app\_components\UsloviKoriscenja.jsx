"use client";

import { useState, useEffect } from "react";
import { createPortal } from "react-dom";

export default function UsloviKoriscenja() {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const modal = isOpen ? (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4"
      onClick={() => setIsOpen(false)}
    >
      <div
        className="bg-[var(--surface)] rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="sticky top-0 bg-[var(--surface)] border-b border-[var(--foreground)]/10 p-6 flex justify-between items-center">
          <h2 className="text-2xl font-bold text-[var(--foreground)]">
            Uslovi korišćenja
          </h2>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 hover:bg-[var(--surface-hover)] rounded-md transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="p-6 space-y-6 text-[var(--foreground)]">
          <section>
            <h3 className="text-xl font-semibold mb-3">
              1. Prihvatanje uslova
            </h3>
            <p className="text-[var(--foreground)]/80 leading-relaxed">
              Korišćenjem Zakazi AI (https://zakaziai.com) prihvatate ove Uslove
              korišćenja u celosti. Ako se ne slažete sa delom Uslova, molimo
              vas da ne koristite platformu.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">
              2. Registracija i nalog
            </h3>
            <ul className="list-disc list-inside space-y-2 text-[var(--foreground)]/80 leading-relaxed">
              <li>
                Saloni se registruju unošenjem validnih podataka (naziv salona,
                kontakt osoba, broj telefona, e‑mail).
              </li>
              <li>
                Vlasnik naloga je odgovoran za bezbednost podataka za prijavu i
                za sve aktivnosti obavljene preko svog naloga.
              </li>
            </ul>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">
              3. Funkcionalnosti servisa
            </h3>
            <ul className="list-disc list-inside space-y-2 text-[var(--foreground)]/80 leading-relaxed">
              <li>
                Automatsko slanje SMS‑a na propuštene pozive sa salonskog broja
                (putem lokalne aplikacije ili VoIP API‑ja).
              </li>
              <li>
                Online zakazivanje termina putem generisanog linka i salonskog
                dashboard‑a.
              </li>
              <li>AI agent koji predlaže slobodne termine klijentima.</li>
              <li>
                Notifikacije (SMS/e‑mail) za potvrdu, podsetnik i obaveštenja o
                promenama termina.
              </li>
            </ul>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">4. Cena i plaćanje</h3>
            <ul className="list-disc list-inside space-y-2 text-[var(--foreground)]/80 leading-relaxed">
              <li>
                Platforma nudi besplatan probni period od 30 dana za sve
                funkcionalnosti.
              </li>
              <li>
                Nakon probnog perioda postoje paketne pretplate (standard,
                standard plus premium, enterprise), čije su cene vidljive na
                https://zakaziai.com/#cene.
              </li>
              <li>
                Plaćanje se obavlja mesečno ili godišnje, putem kreditne kartice
                ili direktnog bankovnog transfera.
              </li>
            </ul>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">
              5. Ograničenje odgovornosti
            </h3>
            <ul className="list-disc list-inside space-y-2 text-[var(--foreground)]/80 leading-relaxed">
              <li>
                Platforma se pruža „kao što jeste", bez garancije da će uvek
                biti dostupna ili bez grešaka.
              </li>
              <li>
                Ne snosimo odgovornost za neposredne ili posredne štete nastale
                korišćenjem servisa, u najvišoj meri dozvoljenoj važećim
                zakonima.
              </li>
            </ul>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">6. Prestanak usluge</h3>
            <ul className="list-disc list-inside space-y-2 text-[var(--foreground)]/80 leading-relaxed">
              <li>
                Saloni mogu u svakom momentu otkazati pretplatu i obrisati
                nalog.
              </li>
              <li>
                Mi zadržavamo pravo da suspendujemo ili obrišemo naloge koji
                krše ove Uslove (npr. spamovanje ili zloupotreba SMS‑a).
              </li>
            </ul>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">
              7. Intelektualna svojina
            </h3>
            <p className="text-[var(--foreground)]/80 leading-relaxed">
              Sav sadržaj, brend i kod platforme su naše autorsko i industrijsko
              vlasništvo. Bez našeg pismenog odobrenja, zabranjeno je kopiranje,
              distribucija ili javno objavljivanje.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">8. Izmene Uslova</h3>
            <p className="text-[var(--foreground)]/80 leading-relaxed">
              Možemo menjati ove Uslove u bilo koje vreme. Nove verzije
              objavljujemo na https://zakaziai.com/terms, a korisnici se o tome
              obaveštavaju putem e‑maila.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">9. Nadležnost</h3>
            <p className="text-[var(--foreground)]/80 leading-relaxed">
              Ovi Uslovi podležu zakonima Republike Srbije. Za sva sporna
              pitanja nadležan je sud u Beogradu.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-3">
              10. Kontakt za pravna pitanja
            </h3>
            <p className="text-[var(--foreground)]/80 leading-relaxed">
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 hover:text-teal-700 transition-colors"
              >
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </div>
    </div>
  ) : null;

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="text-[var(--foreground)]/70 hover:text-[var(--foreground)] transition-colors text-sm"
      >
        Uslovi korišćenja
      </button>

      {mounted && modal && createPortal(modal, document.body)}
    </>
  );
}
