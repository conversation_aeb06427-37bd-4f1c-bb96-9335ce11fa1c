"use client";

import { useState, useEffect } from "react";

function RequestsManager() {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all"); // all, novo, kont<PERSON><PERSON><PERSON>, zavr<PERSON><PERSON>
  const [sortBy, setSortBy] = useState("newest"); // newest, oldest, name
  const [selectedRequest, setSelectedRequest] = useState(null);

  useEffect(() => {
    fetchRequests();
  }, []);

  const fetchRequests = async () => {
    try {
      const response = await fetch("/api/admin/requests");
      const data = await response.json();
      setRequests(data);
    } catch (error) {
      console.error("Error fetching requests:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateRequestStatus = async (id, status) => {
    try {
      const response = await fetch("/api/admin/requests", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id, status }),
      });

      if (response.ok) {
        fetchRequests(); // Refresh data
      }
    } catch (error) {
      console.error("Error updating request:", error);
    }
  };

  const deleteRequest = async (id) => {
    if (!confirm("Da li ste sigurni da želite da obrišete ovaj zahtev?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/requests?id=${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchRequests(); // Refresh data
        setSelectedRequest(null);
      }
    } catch (error) {
      console.error("Error deleting request:", error);
    }
  };

  const filteredRequests = requests.filter((request) => {
    if (filter === "all") return true;
    return request.status === filter;
  });

  const sortedRequests = [...filteredRequests].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return new Date(b.createdAt) - new Date(a.createdAt);
      case "oldest":
        return new Date(a.createdAt) - new Date(b.createdAt);
      case "name":
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  const getStatusColor = (status) => {
    switch (status) {
      case "novo":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "kontaktirano":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "završeno":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case "demo":
        return "Demo zahtev";
      case "plan-change":
        return "Promena plana";
      case "free-trial":
      default:
        return "Besplatni mesec";
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case "demo":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      case "plan-change":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "free-trial":
      default:
        return "bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400";
    }
  };

  if (loading) {
    return (
      <div className="bg-[var(--surface)] rounded-lg shadow p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-500 mx-auto mb-4"></div>
          <p className="text-[var(--foreground)]/70">Učitavanje zahteva...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[var(--surface)] rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-black/5 dark:border-white/10">
        <h2 className="text-xl font-semibold text-[var(--foreground)]">
          Upravljanje zahtevima
        </h2>
        <p className="text-[var(--foreground)]/70 mt-1">
          Zahtevi za demo, besplatni mesec i promene planova
        </p>
      </div>

      {/* Stats */}
      <div className="px-6 py-4 border-b border-black/5 dark:border-white/10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-[var(--foreground)]">
              {requests.length}
            </div>
            <div className="text-sm text-[var(--foreground)]/70">Ukupno</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {requests.filter((r) => r.status === "novo").length}
            </div>
            <div className="text-sm text-[var(--foreground)]/70">Novi</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {requests.filter((r) => r.status === "kontaktirano").length}
            </div>
            <div className="text-sm text-[var(--foreground)]/70">
              Kontaktirano
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {requests.filter((r) => r.status === "završeno").length}
            </div>
            <div className="text-sm text-[var(--foreground)]/70">Završeno</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="px-6 py-4 border-b border-black/5 dark:border-white/10">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="w-full px-3 py-2 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"
            >
              <option value="all">Svi zahtevi</option>
              <option value="novo">Novi</option>
              <option value="kontaktirano">Kontaktirano</option>
              <option value="završeno">Završeno</option>
            </select>
          </div>
          <div className="flex-1">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"
            >
              <option value="newest">Najnoviji prvo</option>
              <option value="oldest">Najstariji prvo</option>
              <option value="name">Po imenu</option>
            </select>
          </div>
        </div>
      </div>

      {/* Requests List */}
      <div className="p-6">
        {sortedRequests.length === 0 ? (
          <div className="text-center py-8">
            <svg
              className="w-12 h-12 text-[var(--foreground)]/30 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <p className="text-[var(--foreground)]/70">
              Nema zahteva za prikaz
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedRequests.map((request) => (
              <div
                key={request.id}
                className="border border-black/10 dark:border-white/10 rounded-lg p-4 hover:bg-[var(--muted)] transition-colors"
              >
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-[var(--foreground)]">
                        {request.name}
                      </h3>
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(
                          request.type
                        )}`}
                      >
                        {getTypeLabel(request.type)}
                      </span>
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          request.status
                        )}`}
                      >
                        {request.status}
                      </span>
                    </div>
                    <div className="text-sm text-[var(--foreground)]/70 space-y-1">
                      {request.type === "plan-change" ? (
                        <>
                          <div>
                            <strong>Biznis:</strong>{" "}
                            {request.businessName || "N/A"}
                          </div>
                          <div>
                            <strong>Kontakt:</strong> {request.email}
                          </div>
                          <div>
                            <strong>Trenutni plan:</strong>{" "}
                            {request.currentPlan}
                          </div>
                          <div>
                            <strong>Željeni plan:</strong>{" "}
                            {request.requestedPlan}
                          </div>
                          <div>
                            <strong>Datum zahteva:</strong>{" "}
                            {new Date(request.createdAt).toLocaleString(
                              "sr-RS"
                            )}
                          </div>
                        </>
                      ) : (
                        <>
                          <div>
                            <strong>Biznis:</strong> {request.businessName} (
                            {request.businessType})
                          </div>
                          <div>
                            <strong>Kontakt:</strong> {request.email} |{" "}
                            {request.phone}
                          </div>
                          <div>
                            <strong>Datum:</strong>{" "}
                            {new Date(request.createdAt).toLocaleString(
                              "sr-RS"
                            )}
                          </div>
                          {request.preferredTime && (
                            <div>
                              <strong>Preferirano vreme:</strong>{" "}
                              {request.preferredTime}
                            </div>
                          )}
                          {request.message && (
                            <div>
                              <strong>Poruka:</strong> {request.message}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <select
                      value={request.status}
                      onChange={(e) =>
                        updateRequestStatus(request.id, e.target.value)
                      }
                      className="px-3 py-1 text-sm border border-black/10 dark:border-white/10 rounded bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-teal-500"
                    >
                      <option value="novo">Novo</option>
                      <option value="kontaktirano">Kontaktirano</option>
                      <option value="završeno">Završeno</option>
                    </select>
                    <button
                      onClick={() => setSelectedRequest(request)}
                      className="px-3 py-1 text-sm bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400 rounded hover:bg-teal-200 dark:hover:bg-teal-900/40 transition-colors"
                    >
                      Detalji
                    </button>
                    <button
                      onClick={() => deleteRequest(request.id)}
                      className="px-3 py-1 text-sm bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 rounded hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
                    >
                      Obriši
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Request Details Modal */}
      {selectedRequest && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-[var(--surface)] rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-black/5 dark:border-white/10">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-[var(--foreground)]">
                Detalji zahteva
              </h3>
              <button
                onClick={() => setSelectedRequest(null)}
                className="p-2 hover:bg-[var(--surface-hover)] rounded-full transition-colors"
              >
                <svg
                  className="w-6 h-6 text-[var(--foreground)]/70"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                    Ime i prezime
                  </label>
                  <p className="text-[var(--foreground)]/80">
                    {selectedRequest.name}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                    Email
                  </label>
                  <p className="text-[var(--foreground)]/80">
                    <a
                      href={`mailto:${selectedRequest.email}`}
                      className="text-teal-600 hover:underline"
                    >
                      {selectedRequest.email}
                    </a>
                  </p>
                </div>

                {/* Conditional fields based on request type */}
                {selectedRequest.type === "plan-change" ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                        Trenutni plan
                      </label>
                      <p className="text-[var(--foreground)]/80">
                        {selectedRequest.currentPlan}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                        Željeni plan
                      </label>
                      <p className="font-semibold text-orange-600">
                        {selectedRequest.requestedPlan}
                      </p>
                    </div>
                    {selectedRequest.businessName && (
                      <div>
                        <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                          Naziv biznisa
                        </label>
                        <p className="text-[var(--foreground)]/80">
                          {selectedRequest.businessName}
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                        Telefon
                      </label>
                      <p className="text-[var(--foreground)]/80">
                        <a
                          href={`tel:${selectedRequest.phone}`}
                          className="text-teal-600 hover:underline"
                        >
                          {selectedRequest.phone}
                        </a>
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                        Tip biznisa
                      </label>
                      <p className="text-[var(--foreground)]/80">
                        {selectedRequest.businessType}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                        Naziv biznisa
                      </label>
                      <p className="text-[var(--foreground)]/80">
                        {selectedRequest.businessName}
                      </p>
                    </div>
                    {selectedRequest.preferredTime && (
                      <div>
                        <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                          Preferirano vreme
                        </label>
                        <p className="text-[var(--foreground)]/80">
                          {selectedRequest.preferredTime}
                        </p>
                      </div>
                    )}
                  </>
                )}

                <div>
                  <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                    Tip zahteva
                  </label>
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(
                      selectedRequest.type
                    )}`}
                  >
                    {getTypeLabel(selectedRequest.type)}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                    Datum zahteva
                  </label>
                  <p className="text-[var(--foreground)]/80">
                    {new Date(selectedRequest.createdAt).toLocaleString(
                      "sr-RS"
                    )}
                  </p>
                </div>
              </div>

              {selectedRequest.message && (
                <div>
                  <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                    Dodatne informacije
                  </label>
                  <p className="text-[var(--foreground)]/80 bg-[var(--muted)] p-3 rounded-lg">
                    {selectedRequest.message}
                  </p>
                </div>
              )}

              <div className="flex justify-between items-center pt-4 border-t border-black/10 dark:border-white/10">
                <div>
                  <label className="block text-sm font-medium text-[var(--foreground)] mb-1">
                    Status
                  </label>
                  <select
                    value={selectedRequest.status}
                    onChange={(e) => {
                      updateRequestStatus(selectedRequest.id, e.target.value);
                      setSelectedRequest({
                        ...selectedRequest,
                        status: e.target.value,
                      });
                    }}
                    className="px-3 py-2 border border-black/10 dark:border-white/10 rounded-lg bg-[var(--background)] text-[var(--foreground)] focus:ring-2 focus:ring-teal-500"
                  >
                    <option value="novo">Novo</option>
                    <option value="kontaktirano">Kontaktirano</option>
                    <option value="završeno">Završeno</option>
                  </select>
                </div>
                <button
                  onClick={() => deleteRequest(selectedRequest.id)}
                  className="px-4 py-2 bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
                >
                  Obriši zahtev
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RequestsManager;
