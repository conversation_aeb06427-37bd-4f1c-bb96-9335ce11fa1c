import Nav from "./_components/Nav";
import Hero from "./_components/Hero";
import Footer from "./_components/Footer";
import <PERSON><PERSON><PERSON> from "./_components/Funkcije";
import Cena from "./_components/Cena";
import CTA from "./_components/CTA";
import Zakoga from "./_components/Zakoga";
import <PERSON> from "next/head";
export default function Home() {
  return (
    <>
      <Head>
        <meta
          name="google-site-verification"
          content="y49ob3BkPt_HMuum8nQoYQsRYuOtQkacnpz_C_Tgvv4"
        />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#14b8a6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Zakaži AI" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  const savedTheme = localStorage.getItem('theme');
                  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                  if (savedTheme === 'dark' || (savedTheme !== 'light' && systemPrefersDark)) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                } catch (e) {
                  console.error('Error applying theme:', e);
                }
              })();
            `,
          }}
        />
      </Head>
      <div className="min-h-screen flex flex-col font-[family-name:var(--font-geist-sans)]">
        <Nav />
        <Hero />
        <Zakoga />
        <Funkcije />
        <Cena />
        <CTA />
        <Footer />
      </div>
    </>
  );
}
