// models/Salon.js
import mongoose from "mongoose";
import { customAlphabet } from "nanoid";

const nanoid = customAlphabet("abcdefghijklmnopqrstuvwxyz0123456789", 8);

const TimeRangeSchema = new mongoose.Schema({
  from: { type: String, required: true }, // "HH:mm"
  to: { type: String, required: true },
});

const DayScheduleSchema = new mongoose.Schema({
  closed: { type: Boolean, default: false },
  from: { type: String, default: "09:00" },
  to: { type: String, default: "17:00" },
  breaks: { type: [TimeRangeSchema], default: [] },
});

const WorkingHoursSchema = new mongoose.Schema({
  ponedeljak: { type: DayScheduleSchema, default: () => ({}) },
  utorak: { type: DayScheduleSchema, default: () => ({}) },
  sreda: { type: DayScheduleSchema, default: () => ({}) },
  cetvrtak: { type: DayScheduleSchema, default: () => ({}) },
  petak: { type: DayScheduleSchema, default: () => ({}) },
  subota: {
    type: DayScheduleSchema,
    default: () => ({ from: "10:00", to: "14:00", breaks: [] }),
  },
  nedelja: {
    type: DayScheduleSchema,
    default: () => ({ closed: true, breaks: [] }),
  },
});

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true }, // promenjeno ovde
  email: { type: String, required: true, unique: true, lowercase: true },
  password: { type: String, required: true },
  role: { type: String, default: "user" },
  phone: { type: String },
  businessName: { type: String },
  businessType: { type: String, default: "salon" },
  plan: {
    type: String,
    enum: ["standard", "standard-plus", "premium", "enterprise"],
    default: "standard",
  },
  workingHours: {
    type: WorkingHoursSchema,
    default: () => ({ from: "10:00", to: "14:00", breaks: [] }),
  },
  createdAt: { type: Date, default: Date.now },
  services: [
    {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        default: () => new mongoose.Types.ObjectId(),
      },
      name: { type: String, required: true },
      description: { type: String },
      durationMins: { type: Number, required: true, default: 30 },
      price: { type: String },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date },
    },
  ],
  urlSlug: { type: String, unique: true },
  slug: { type: String, unique: true, default: () => nanoid() },
});

export default mongoose.models.User ||
  mongoose.model("User", UserSchema, "user");
