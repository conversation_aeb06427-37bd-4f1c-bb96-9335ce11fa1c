"use client";

import { useIntersectionObserver } from "../hooks/useIntersectionObserver";

function Funkcije() {
  const [ref, isIntersecting, hasIntersected] = useIntersectionObserver();
  const features = [
    {
      title: "24/7 Online zakazivanje",
      description:
        "Automatizujte komunikaciju sa klijentima uz inteligentnog agenta koji razume poruke i zakazuje termine umesto vas.",
      icon: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
      delay: "animation-delay-200",
    },
    {
      title: "Upravljanje uslugama",
      description:
        "<PERSON><PERSON> do<PERSON>, uređujte i organizujte svoje usluge sa cenama i trajanjem. Klijenti mogu birati između različitih opcija pri zakazivanju.",
      icon: "M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01",
      delay: "animation-delay-400",
    },
    {
      title: "Analitička kontrolna tabla",
      description:
        "Steknite uvid u svoje poslovanje uz detaljne izveštaje o rezervacijama, prihodima i zadržavanju klijenata.",
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
      delay: "animation-delay-600",
    },
    {
      title: "AI Asistent za zakazivanje",
      description:
        "Napredni AI asistent koji automatski obrađuje zahteve za zakazivanje i pomaže klijentima da pronađu idealan termin.",
      icon: "M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z",
      delay: "animation-delay-800",
    },
  ];

  return (
    <section
      ref={ref}
      id="features"
      className="py-16 sm:py-24 bg-[var(--muted)] relative overflow-hidden"
    >
      {/* Background decoration */}
      <div className="absolute inset-0  bg-[var(--muted)] from-teal-50/50 to-blue-50/50 dark:from-teal-900/10 dark:to-blue-900/10"></div>

      {/* Booking-related decorative elements */}
      <div className="absolute top-10 left-1/4 w-24 h-24 opacity-40 animate-float animation-delay-1000">
        <svg
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-full h-full text-teal-400"
        >
          <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
        </svg>
      </div>

      <div className="absolute top-1/2 left-10 w-20 h-20 opacity-40 animate-float animation-delay-2000">
        <svg
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-full h-full text-purple-400"
        >
          <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z" />
        </svg>
      </div>

      <div className="absolute top-1/3 right-10 w-22 h-22 opacity-40 animate-float animation-delay-4000">
        <svg
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-full h-full text-green-400"
        >
          <path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div
          className={`lg:text-center transition-all duration-1000 ${
            hasIntersected ? "animate-fade-in-up" : "opacity-0 translate-y-10"
          }`}
        >
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-[var(--foreground)] sm:text-4xl">
            Sve što Vam je potrebno za upravljanje terminima
          </p>
          <p
            className={`mt-4 max-w-2xl text-xl text-[var(--foreground)]/70 lg:mx-auto transition-all duration-1000 delay-200 ${
              hasIntersected ? "animate-fade-in-up" : "opacity-0 translate-y-10"
            }`}
          >
            Naša platforma je dizajnirana da učini zakazivanje termina
            jednostavnim i za Vas i za Vaše klijente.
          </p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`pt-6 transition-all duration-1000 ${
                  hasIntersected
                    ? `animate-fade-in-up ${feature.delay}`
                    : "opacity-0 translate-y-10"
                }`}
                style={{
                  transitionDelay: hasIntersected
                    ? `${(index + 2) * 200}ms`
                    : "0ms",
                }}
              >
                <div className="group flow-root bg-[var(--surface)] rounded-lg px-6 pb-8 transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 hover:bg-gradient-to-br hover:from-[var(--surface)] hover:to-teal-50/50 dark:hover:to-teal-900/20 border border-transparent hover:border-teal-200/50 dark:hover:border-teal-700/50">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-teal-500 to-blue-600 rounded-md shadow-lg group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300 animate-pulse-slow">
                        <svg
                          className="h-6 w-6 text-white transition-transform duration-300 group-hover:rotate-12"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={feature.icon}
                          />
                        </svg>
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-[var(--foreground)] tracking-tight group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="mt-5 text-base text-[var(--foreground)]/70 group-hover:text-[var(--foreground)]/90 transition-colors duration-300">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export default Funkcije;
