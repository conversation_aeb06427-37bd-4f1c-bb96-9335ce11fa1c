import { NextResponse } from "next/server";
import dbConnect from "../../../auth/lib/mongoose";
import User from "../../../auth/models/User";
import AIUsage from "../../../auth/models/AIUsage";

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get("days")) || 30;
    const userId = searchParams.get("userId"); // Optional - if provided, show only that user

    // Povezivanje sa bazom
    await dbConnect();

    // Računanje datuma
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    let query = {
      createdAt: { $gte: startDate },
    };

    // Ako je specificiran korisnik, filtriraj po njemu
    if (userId && userId !== "all") {
      query.userId = userId;
    }

    // Dohvatanje AI usage podataka
    const usageData = await AIUsage.find(query)
      .populate("userId", "name email plan")
      .sort({ createdAt: -1 });

    // Dohvatanje svih korisnika za dropdown
    const allUsers = await User.find({}, "name email plan").sort({ name: 1 });

    // Računanje ukupnih statistika
    const totalStats = {
      totalTokens: usageData.reduce((sum, u) => sum + u.tokensUsed, 0),
      totalCost: usageData.reduce((sum, u) => sum + u.cost, 0),
      requestCount: usageData.length,
      byFeature: {},
      byDay: {},
    };

    // Grupisanje po feature-u
    usageData.forEach((u) => {
      if (!totalStats.byFeature[u.feature]) {
        totalStats.byFeature[u.feature] = {
          tokens: 0,
          cost: 0,
          requests: 0,
        };
      }
      totalStats.byFeature[u.feature].tokens += u.tokensUsed;
      totalStats.byFeature[u.feature].cost += u.cost;
      totalStats.byFeature[u.feature].requests += 1;
    });

    // Grupisanje po danima
    usageData.forEach((u) => {
      const day = u.createdAt.toISOString().split("T")[0];
      if (!totalStats.byDay[day]) {
        totalStats.byDay[day] = {
          tokens: 0,
          cost: 0,
          requests: 0,
        };
      }
      totalStats.byDay[day].tokens += u.tokensUsed;
      totalStats.byDay[day].cost += u.cost;
      totalStats.byDay[day].requests += 1;
    });

    // Statistike po korisnicima (samo ako nije specificiran jedan korisnik)
    let userStats = [];
    let activeUsers = 0;

    if (!userId || userId === "all") {
      const userStatsMap = {};

      usageData.forEach((u) => {
        if (u.userId) {
          const userIdStr = u.userId._id.toString();
          if (!userStatsMap[userIdStr]) {
            userStatsMap[userIdStr] = {
              userId: userIdStr,
              userName: u.userId.name,
              userEmail: u.userId.email,
              userPlan: u.userId.plan,
              totalTokens: 0,
              totalCost: 0,
              requestCount: 0,
            };
          }
          userStatsMap[userIdStr].totalTokens += u.tokensUsed;
          userStatsMap[userIdStr].totalCost += u.cost;
          userStatsMap[userIdStr].requestCount += 1;
        }
      });

      userStats = Object.values(userStatsMap).sort(
        (a, b) => b.totalCost - a.totalCost
      ); // Sortiraj po trošku

      activeUsers = userStats.length;
    }

    return NextResponse.json({
      totalStats,
      userStats,
      activeUsers,
      period: `${days} dana`,
      users: allUsers,
      success: true,
    });
  } catch (error) {
    console.error("Admin AI Usage Stats Error:", error);
    return NextResponse.json(
      { error: "Greška pri dohvatanju AI statistika" },
      { status: 500 }
    );
  }
}
