"use client";

import React, { useState, useEffect } from "react";
import { format, isToday, parseISO } from "date-fns";
import { getPlanLimits } from "../../utils/planLimits";
import AIAnalytics from "./AIAnalytics";

export default function Dashboard({ user, setActiveSection }) {
  const [bookings, setBookings] = useState([]);
  const [todayCount, setTodayCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [monthlyCount, setMonthlyCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Funkcija za učitavanje zakazivanja
  const loadBookings = async () => {
    setIsLoading(true);
    try {
      const userId = user?.user?._id || user?._id;

      if (!userId) {
        setIsLoading(false);
        return;
      }

      const response = await fetch(`/api/bookings?userId=${userId}`);

      if (!response.ok) {
        console.error("Greška pri učitavanju zakazivanja");
        setIsLoading(false);
        return;
      }

      const data = await response.json();

      if (!data.bookings || !Array.isArray(data.bookings)) {
        setBookings([]);
        setTotalCount(0);
        setTodayCount(0);
        setIsLoading(false);
        return;
      }

      // Filtriramo da uklonimo nevažeće vrednosti
      const validBookings = data.bookings.filter(
        (booking) =>
          booking && typeof booking === "object" && booking.clientName
      );

      setBookings(validBookings);
      setTotalCount(validBookings.length);

      // Brojimo današnja zakazivanja
      const todayBookings = validBookings.filter((booking) => {
        if (!booking.date) return false;
        try {
          const bookingDate = parseISO(booking.date);
          return isToday(bookingDate);
        } catch (err) {
          return false;
        }
      });

      setTodayCount(todayBookings.length);

      // Brojimo mesečna zakazivanja (od prvog do poslednjeg dana u mesecu)
      const today = new Date();
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const startOfNextMonth = new Date(
        today.getFullYear(),
        today.getMonth() + 1,
        1
      );

      const monthlyBookings = validBookings.filter((booking) => {
        if (!booking.date) return false;
        try {
          const bookingDate = parseISO(booking.date);
          return bookingDate >= startOfMonth && bookingDate < startOfNextMonth;
        } catch (err) {
          return false;
        }
      });

      setMonthlyCount(monthlyBookings.length);
    } catch (error) {
      console.error("Greška pri učitavanju zakazivanja:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Učitaj zakazivanja kada se komponenta učita
  useEffect(() => {
    loadBookings();
  }, [user]);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Dobrodošli, {user?.user.name}!
      </h2>
      <p className="text-[var(--foreground)]/70 mb-4">
        Ovo je vaša kontrolna tabla za upravljanje zakazivanjima.
      </p>

      {/* Plan Info */}
      {(() => {
        const userPlan = user?.user?.plan || user?.plan || "standard";
        const planLimits = getPlanLimits(userPlan);

        return (
          <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-green-800">
                  Trenutni plan: {planLimits.name}
                </h3>
                <div className="text-sm text-green-600 mt-1">
                  {planLimits.monthlyLimit ? (
                    <span>
                      Mesečni limit: {planLimits.monthlyLimit} termina
                    </span>
                  ) : (
                    <span>Mesečni limit: neograničeno</span>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-green-800">
                  {planLimits.price}
                </div>
              </div>
            </div>
          </div>
        );
      })()}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[var(--background)] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
          <h3 className="font-medium text-[var(--foreground)] mb-2">
            Današnja zakazivanja
          </h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-bold text-blue-600">
              {isLoading ? "..." : todayCount}
            </p>
            {!isLoading && (
              <div className="ml-2 text-sm text-[var(--foreground)]/70">
                <div>{todayCount === 1 ? "termin" : "termina"}</div>
              </div>
            )}
          </div>
        </div>
        <div className="bg-[var(--background)] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
          <h3 className="font-medium text-[var(--foreground)] mb-2">
            Ukupno zakazivanja
          </h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-bold text-purple-600">
              {isLoading ? "..." : totalCount}
            </p>
            {!isLoading && (
              <div className="ml-2 text-sm text-[var(--foreground)]/70">
                <div>{totalCount === 1 ? "termin" : "termina"}</div>
                <div className="text-xs mt-1 opacity-75">
                  Ukupno svih vremena
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="bg-[var(--background)] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
          <h3 className="font-medium text-[var(--foreground)] mb-2">
            Mesečna zakazivanja
          </h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-bold text-green-600">
              {isLoading
                ? "..."
                : (() => {
                    const userPlan =
                      user?.user?.plan || user?.plan || "standard";
                    const planLimits = getPlanLimits(userPlan);

                    if (planLimits.monthlyLimit) {
                      return `${monthlyCount}/${planLimits.monthlyLimit}`;
                    } else {
                      return monthlyCount;
                    }
                  })()}
            </p>
            {!isLoading && (
              <div className="ml-2 text-sm text-[var(--foreground)]/70">
                <div>
                  {(() => {
                    const userPlan =
                      user?.user?.plan || user?.plan || "standard";
                    const planLimits = getPlanLimits(userPlan);

                    if (planLimits.monthlyLimit) {
                      return monthlyCount === 1 ? "termin" : "termina";
                    } else {
                      return `${
                        monthlyCount === 1 ? "termin" : "termina"
                      } (neograničeno)`;
                    }
                  })()}
                </div>
                <div className="text-xs mt-1 opacity-75">
                  Resetuje se 1. u mesecu
                </div>
              </div>
            )}
          </div>
          {/* Progress bar za mesečne termine */}
          {!isLoading &&
            (() => {
              const userPlan = user?.user?.plan || user?.plan || "standard";
              const planLimits = getPlanLimits(userPlan);

              if (planLimits.monthlyLimit) {
                const percentage = Math.min(
                  (monthlyCount / planLimits.monthlyLimit) * 100,
                  100
                );
                const isNearLimit = percentage > 80;

                return (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          isNearLimit ? "bg-red-500" : "bg-green-500"
                        }`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}
        </div>
      </div>

      <div className="mt-8">
        <h3 className="text-lg font-medium text-[var(--foreground)] mb-4">
          Brze akcije
        </h3>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => setActiveSection("appointments")}
            className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
          >
            Dodaj novo zakazivanje
          </button>
          <button
            onClick={() => setActiveSection("services")}
            className="px-4 py-2 bg-[var(--background)] text-[var(--foreground)] border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
          >
            Upravljaj uslugama
          </button>
          <button
            onClick={() => setActiveSection("settings")}
            className="px-4 py-2 bg-[var(--background)] text-[var(--foreground)] border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
          >
            Podešavanja naloga
          </button>
        </div>
      </div>

      {/* AI Analytics Section - Only for Standard Plus and above */}
      {(() => {
        const userPlan = user?.user?.plan || user?.plan || "standard";
        if (userPlan !== "standard") {
          return (
            <div className="mt-8">
              <AIAnalytics user={user} />
            </div>
          );
        }
        return null;
      })()}
    </div>
  );
}
