"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/contexts/AuthProvider";

export default function WorkingHours({ user }) {
  const { refreshUserData } = useAuth();
  const [workingHours, setWorkingHours] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [editingDay, setEditingDay] = useState(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ text: "", type: "" });
  const [showBreaksModal, setShowBreaksModal] = useState(false);
  const [editingBreaks, setEditingBreaks] = useState([]);
  const [currentBreak, setCurrentBreak] = useState({ from: "", to: "" });

  // Prevod dana na srpski
  const dayTranslations = {
    ponedeljak: "Ponedeljak",
    utorak: "Utorak",
    sreda: "<PERSON>eda",
    cetvrtak: "<PERSON>etvrta<PERSON>",
    petak: "Petak",
    subota: "Subota",
    nedelja: "Nedelja",
  };

  // Podrazumevano radno vreme ako nije definisano
  const defaultWorkingHours = {
    ponedeljak: { from: "09:00", to: "17:00", closed: false, breaks: [] },
    utorak: { from: "09:00", to: "17:00", closed: false, breaks: [] },
    sreda: { from: "09:00", to: "17:00", closed: false, breaks: [] },
    cetvrtak: { from: "09:00", to: "17:00", closed: false, breaks: [] },
    petak: { from: "09:00", to: "17:00", closed: false, breaks: [] },
    subota: { from: "09:00", to: "14:00", closed: false, breaks: [] },
    nedelja: { from: "09:00", to: "14:00", closed: true, breaks: [] },
  };

  // Funkcija za učitavanje radnog vremena direktno sa servera
  const loadWorkingHours = async () => {
    try {
      const userId = user?.user?._id || user?._id;
      if (!userId) return;

      const response = await fetch(`/api/working-hours/get?userId=${userId}`);

      if (!response.ok) {
        throw new Error("Greška pri učitavanju radnog vremena");
      }

      const data = await response.json();

      // Filter function to remove non-day properties like _id
      const filterWorkingHours = (hours) => {
        if (!hours) return defaultWorkingHours;

        const filteredHours = {};
        Object.keys(dayTranslations).forEach((day) => {
          // If day exists in working hours, use it; otherwise use default
          filteredHours[day] = hours[day] || defaultWorkingHours[day];
        });
        return filteredHours;
      };

      if (data.workingHours) {
        setWorkingHours(filterWorkingHours(data.workingHours));
      } else {
        // Ako nema definisano radno vreme na serveru, koristimo podatke iz user objekta ili podrazumevano
        const userWorkingHours = user?.user?.workingHours || user?.workingHours;
        setWorkingHours(
          filterWorkingHours(userWorkingHours) || defaultWorkingHours
        );
      }
    } catch (err) {
      console.error("Greška pri učitavanju radnog vremena:", err);
      // Fallback na podatke iz user objekta
      const userWorkingHours = user?.user?.workingHours || user?.workingHours;
      setWorkingHours(
        filterWorkingHours(userWorkingHours) || defaultWorkingHours
      );
    }
  };

  // Učitavanje radnog vremena
  useEffect(() => {
    if (user) {
      loadWorkingHours();
    }
  }, [user]);

  // Počni uređivanje radnog vremena za određeni dan
  const handleEditDay = (day) => {
    setEditingDay(day);
    setEditingBreaks(workingHours[day]?.breaks || []);
    setCurrentBreak({ from: "", to: "" });
  };

  // Dodavanje nove pauze
  const handleAddBreak = () => {
    if (!currentBreak.from || !currentBreak.to) {
      setMessage({
        text: "Morate definisati početak i kraj pauze",
        type: "error",
      });
      return;
    }

    // Validacija vremena (kraj pauze mora biti nakon početka)
    const fromTime = currentBreak.from.split(":").map(Number);
    const toTime = currentBreak.to.split(":").map(Number);

    const fromMinutes = fromTime[0] * 60 + fromTime[1];
    const toMinutes = toTime[0] * 60 + toTime[1];

    if (fromMinutes >= toMinutes) {
      setMessage({
        text: "Kraj pauze mora biti nakon početka pauze",
        type: "error",
      });
      return;
    }

    // Provera preklapanja sa postojećim pauzama
    const isOverlapping = editingBreaks.some((breakTime) => {
      const breakFromTime = breakTime.from.split(":").map(Number);
      const breakToTime = breakTime.to.split(":").map(Number);

      const breakFromMinutes = breakFromTime[0] * 60 + breakFromTime[1];
      const breakToMinutes = breakToTime[0] * 60 + breakToTime[1];

      // Provera da li se nova pauza preklapa sa postojećom
      return (
        (fromMinutes < breakToMinutes && toMinutes > breakFromMinutes) ||
        (breakFromMinutes < toMinutes && breakToMinutes > fromMinutes)
      );
    });

    if (isOverlapping) {
      setMessage({
        text: "Nova pauza se preklapa sa postojećom pauzom",
        type: "error",
      });
      return;
    }

    // Dodaj novu pauzu i sortiraj ih po vremenu početka
    const newBreaks = [...editingBreaks, { ...currentBreak }].sort((a, b) => {
      const aTime = a.from.split(":").map(Number);
      const bTime = b.from.split(":").map(Number);

      const aMinutes = aTime[0] * 60 + aTime[1];
      const bMinutes = bTime[0] * 60 + bTime[1];

      return aMinutes - bMinutes;
    });

    setEditingBreaks(newBreaks);
    setCurrentBreak({ from: "", to: "" });
    setMessage({ text: "", type: "" });
  };

  // Brisanje pauze
  const handleDeleteBreak = (index) => {
    const newBreaks = [...editingBreaks];
    newBreaks.splice(index, 1);
    setEditingBreaks(newBreaks);
  };

  // Primena promena pauza na odabrani dan
  const applyBreaksChanges = () => {
    const updatedWorkingHours = {
      ...workingHours,
      [editingDay]: {
        ...workingHours[editingDay],
        breaks: [...editingBreaks],
      },
    };

    setWorkingHours(updatedWorkingHours);
    setShowBreaksModal(false);
  };

  // Promena statusa radnog dana (otvoreno/zatvoreno)
  const handleClosedChange = (day, closed) => {
    setWorkingHours({
      ...workingHours,
      [day]: {
        ...workingHours[day],
        closed,
      },
    });
  };

  // Promena vremena radnog dana
  const handleTimeChange = (day, field, value) => {
    setWorkingHours({
      ...workingHours,
      [day]: {
        ...workingHours[day],
        [field]: value,
      },
    });
  };

  // Čuvanje promena radnog vremena na serveru
  const handleSaveWorkingHours = async () => {
    setLoading(true);
    setMessage({ text: "", type: "" });

    try {
      const userId = user.user?._id || user._id;

      // Filter out non-day properties like _id
      const filteredWorkingHours = {};
      Object.keys(dayTranslations).forEach((day) => {
        if (workingHours[day]) {
          filteredWorkingHours[day] = workingHours[day];
        }
      });

      const response = await fetch("/api/working-hours/update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          workingHours: filteredWorkingHours,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Greška pri čuvanju radnog vremena");
      }

      // Osvežavanje podataka sa servera - prvo lokalno
      await loadWorkingHours();

      // Zatim osvežavamo korisnički kontekst za sve komponente
      await refreshUserData();

      setMessage({
        text: "Radno vreme je uspešno ažurirano",
        type: "success",
      });

      setIsEditing(false);
    } catch (err) {
      console.error("Greška pri čuvanju radnog vremena:", err);
      setMessage({
        text: err.message || "Došlo je do greške pri čuvanju radnog vremena",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Formatiranje prikaza radnog vremena
  const formatWorkingHours = (hours) => {
    if (hours.closed) {
      return <span className="text-red-500">Zatvoreno</span>;
    }

    return (
      <span>
        {hours.from} - {hours.to}
        {hours.breaks && hours.breaks.length > 0 && (
          <span className="ml-2 text-[var(--foreground)]/60 text-sm">
            ({hours.breaks.length}{" "}
            {hours.breaks.length === 2 ||
            hours.breaks.length === 3 ||
            hours.breaks.length === 4
              ? "pauze"
              : "pauza"}
            )
          </span>
        )}
      </span>
    );
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-[var(--foreground)]">
        Radno vreme
      </h2>
      <div className="bg-[var(--surface)] p-6 rounded-lg shadow">
        {message.text && (
          <div
            className={`p-3 mb-4 rounded-md ${
              message.type === "success"
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            }`}
          >
            {message.text}
          </div>
        )}

        <p className="text-[var(--foreground)]/70 mb-6">
          Ovde možete postaviti vaše radno vreme i pauze tokom radnog dana.
        </p>

        {isEditing ? (
          <div className="space-y-6">
            {Object.entries(workingHours)
              .filter(([day, _]) => dayTranslations[day]) // Filter only valid days
              .map(([day, hours]) => (
                <div
                  key={day}
                  className="p-4 border border-[var(--foreground)]/10 rounded-lg bg-[var(--background)]"
                >
                  <div className="flex justify-between items-center mb-4">
                    <div className="font-medium">
                      {dayTranslations[day] || day}
                    </div>
                    <div className="flex items-center space-x-2">
                      <label className="inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={!hours.closed}
                          onChange={(e) =>
                            handleClosedChange(day, !e.target.checked)
                          }
                          className="form-checkbox rounded text-blue-600"
                        />
                        <span className="ml-2 text-sm">Radno</span>
                      </label>
                    </div>
                  </div>

                  {!hours.closed && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          Od
                        </label>
                        <input
                          type="time"
                          value={hours.from || ""}
                          onChange={(e) =>
                            handleTimeChange(day, "from", e.target.value)
                          }
                          className="w-full p-2 border rounded-md bg-[var(--background)]"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          Do
                        </label>
                        <input
                          type="time"
                          value={hours.to || ""}
                          onChange={(e) =>
                            handleTimeChange(day, "to", e.target.value)
                          }
                          className="w-full p-2 border rounded-md bg-[var(--background)]"
                        />
                      </div>
                    </div>
                  )}

                  {!hours.closed && (
                    <div className="mt-4">
                      <button
                        type="button"
                        onClick={() => {
                          handleEditDay(day);
                          setShowBreaksModal(true);
                        }}
                        className="px-3 py-1 text-sm bg-[var(--surface)] hover:bg-[var(--surface-hover)] text-[var(--foreground)] rounded transition-colors"
                      >
                        {hours.breaks && hours.breaks.length > 0
                          ? `Uredi pauze (${hours.breaks.length})`
                          : "Dodaj pauze"}
                      </button>
                    </div>
                  )}
                </div>
              ))}

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
                disabled={loading}
              >
                Otkaži
              </button>
              <button
                onClick={handleSaveWorkingHours}
                className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
                disabled={loading}
              >
                {loading ? "Čuvanje..." : "Sačuvaj radno vreme"}
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {Object.entries(workingHours)
                .filter(([day, _]) => dayTranslations[day]) // Filter only valid days
                .map(([day, hours]) => (
                  <div
                    key={day}
                    className="flex justify-between items-center border-b border-[var(--foreground)]/10 pb-3"
                  >
                    <div className="font-medium">
                      {dayTranslations[day] || day}
                    </div>
                    <div className="flex items-center">
                      {formatWorkingHours(hours)}
                    </div>
                  </div>
                ))}
            </div>
            <div className="mt-6">
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
              >
                Izmeni radno vreme
              </button>
            </div>
          </>
        )}

        {/* Modal za upravljanje pauzama */}
        {showBreaksModal && editingDay && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div
              className="fixed inset-0 bg-black/30 backdrop-blur-sm"
              onClick={() => setShowBreaksModal(false)}
            ></div>
            <div className="relative min-h-screen flex items-center justify-center p-4">
              <div className="relative bg-[var(--background)] rounded-lg shadow-xl max-w-md w-full p-6">
                <h3 className="text-lg font-semibold mb-4">
                  Pauze za {dayTranslations[editingDay] || editingDay}
                </h3>

                {message.text && (
                  <div
                    className={`p-3 mb-4 rounded-md ${
                      message.type === "success"
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    }`}
                  >
                    {message.text}
                  </div>
                )}

                <div className="mb-4">
                  <div className="grid grid-cols-2 gap-3 mb-2">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Početak pauze
                      </label>
                      <input
                        type="time"
                        value={currentBreak.from}
                        onChange={(e) =>
                          setCurrentBreak({
                            ...currentBreak,
                            from: e.target.value,
                          })
                        }
                        className="w-full p-2 border rounded-md bg-[var(--background)]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Kraj pauze
                      </label>
                      <input
                        type="time"
                        value={currentBreak.to}
                        onChange={(e) =>
                          setCurrentBreak({
                            ...currentBreak,
                            to: e.target.value,
                          })
                        }
                        className="w-full p-2 border rounded-md bg-[var(--background)]"
                      />
                    </div>
                  </div>
                  <button
                    onClick={handleAddBreak}
                    className="w-full px-4 py-2 bg-[var(--surface)] hover:bg-[var(--surface-hover)] text-[var(--foreground)] rounded-md transition-colors"
                  >
                    Dodaj pauzu
                  </button>
                </div>

                <div className="max-h-[300px] overflow-y-auto">
                  {editingBreaks.length === 0 ? (
                    <p className="text-center py-3 text-[var(--foreground)]/60">
                      Nema definisanih pauza.
                    </p>
                  ) : (
                    <ul className="space-y-2">
                      {editingBreaks.map((breakTime, index) => (
                        <li
                          key={index}
                          className="flex justify-between items-center p-2 bg-[var(--surface)] rounded-md"
                        >
                          <span>
                            {breakTime.from} - {breakTime.to}
                          </span>
                          <button
                            onClick={() => handleDeleteBreak(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            ✕
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                <div className="flex justify-end space-x-2 mt-4">
                  <button
                    onClick={() => setShowBreaksModal(false)}
                    className="px-4 py-2 border border-[var(--foreground)]/10 rounded-md hover:bg-[var(--surface-hover)] transition-colors"
                  >
                    Otkaži
                  </button>
                  <button
                    onClick={applyBreaksChanges}
                    className="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-md transition-colors"
                  >
                    Primeni promene
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
