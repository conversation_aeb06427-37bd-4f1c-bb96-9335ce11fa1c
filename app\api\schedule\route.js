import { NextResponse } from "next/server";

export async function POST(request) {
  const ip = request.ip || 
             request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';

  const body = await request.json();
  const { date, time, service, clientName, clientPhone } = body;

  // Validacija
  if (!date || !time || !service || !clientName || !clientPhone) {
    return NextResponse.json(
      { error: "Sva polja su obavezna za zakazivanje" },
      { status: 400 }
    );
  }

  // Simulacija zakazivanja termina
  console.log('Schedule appointment:', {
    date,
    time,
    service,
    clientName,
    clientPhone,
    ip,
    timestamp: new Date().toISOString()
  });

  // Ovde bi trebalo dodati logiku za čuvanje termina u bazu
  // await saveAppointment({ date, time, service, clientName, clientPhone });

  return NextResponse.json({
    success: true,
    message: `Te<PERSON><PERSON> je uspe<PERSON>no zakazan za ${date} u ${time}`,
    appointmentId: `APT-${Date.now()}`,
    timestamp: new Date().toISOString()
  });
}

export async function GET(request) {
  const ip = request.ip || 
             request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';

  // Simulacija dobijanja dostupnih termina
  const availableSlots = [
    "09:00", "10:00", "11:00", "14:00", "15:00", "16:00"
  ];

  return NextResponse.json({
    success: true,
    availableSlots,
    date: new Date().toISOString().split('T')[0],
    ip,
    timestamp: new Date().toISOString()
  });
}
